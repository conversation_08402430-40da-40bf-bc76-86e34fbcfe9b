<!-- markdownlint-disable MD013 -->
# Generate the changelog for a release

## Prerequisite

1. Prepare your [Github Token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)

1. Install the Github python dependencies needed to generate the changelog.

    ```console
    pip install PyGithub
    ```

## Generate release notes

1. Run the following command and fetch oneline git commits from the last release (v0.3.0) to current release (v0.4.0).

    ```console
    git log v0.3.0..v0.4.0 --oneline
    ```

    You may need to run the following command first:

    ```console
    git fetch --tags
    ```

1. Copy the above commit history to `scripts/changelog-generator.py` and replace `<your_github_token>` with your Github token.
Run the script to generate changelogs.

    ```python
    from github import Github
    import re


    class ChangelogGenerator:
        def __init__(self, github_repo):
            # Replace <your_github_token> with your Github Token
            self._github = Github('<your_github_token>')
            self._github_repo = self._github.get_repo(github_repo)

        def generate(self, pr_id):
            pr = self._github_repo.get_pull(pr_id)

            return "{title} ([#{pr_id}]({pr_link}), @{user})".format(
                title=pr.title,
                pr_id=pr_id,
                pr_link=pr.html_url,
                user=pr.user.login
            )


    # generated by `git log <oldTag>..<newTag> --oneline`
    payload = '''
    7374e2c [RayService] Skip update events without change (#811) (#825)
    7f83353 Switch to 0.4.0 and eliminate Chart app versions. (#810)
    86b0af2 Remove ingress.enabled from KubeRay operator chart (#812) (#816)
    c1cbaed Update chart versions for 0.4.0-rc.0 (#804)
    84a70f1 Update image tags. (#784)
    d760b9c [helm] Add memory limits and resource documentation. (#789) (#798)
    16905df [Feature] Improve the observability of integration tests (#775) (#796)
    83aab82 [CI] Pin go version in CRD consistency check (#794) (#797)
    ....
    '''

    g = ChangelogGenerator("ray-project/kuberay")
    for pr_match in re.finditer(r"#(\d+)", payload):
        pr_id = int(pr_match.group(1))
        print("* {}".format(g.generate(pr_id)))
    ```

1. To create the release notes, save the output of the script. Modify the script's output as follows.
    - Remove extraneous data, such as commits with tag information or links to other PRs, e.g.

    ```text
    - c1cbaed (tag: v0.4.0-rc.0) Update chart versions for 0.4.0-rc.0 (#804) -> c1cbaed Update chart versions for 0.4.0-rc.0 (#804)
    - 86b0af2 Remove ingress.enabled from KubeRay operator chart (#812) (#816) -> 86b0af2 Remove ingress.enabled from KubeRay operator chart (#816)
    ```

    - Group commits by category e.g. `KubeRay Operator`, `Documentation`, etc. (The choice of categories is at the release manager's discretion.)
    - Add a section summarizing important changes.
    - Add a section listing individuals who contributed to the release.

1. Cut the release from tags and add the release notes from the last step. For an example, see the [v0.3.0 release notes](https://github.com/ray-project/kuberay/releases/tag/v0.3.0).

1. Send a PR to update [CHANGELOG.md](https://github.com/ray-project/kuberay/blob/master/CHANGELOG.md). The changelog should be updated by prepending the new release notes.
