# KubeRay Community Governance

## Becoming a Committer

Nothing in this document is guaranteed, but it provides a clear path for contributors toward becoming committers.

A committer is a trusted member of the KubeRay community with a long-term commitment who has the ownership to
offload the community's workloads and make KubeRay thrive. To elaborate,

* **Offload community’s workloads:** A committer should be able to deliver high-quality code which doesn’t require a lot
  of code reviews, uphold high-quality code reviews, answer questions on GitHub and Slack, and release new KubeRay
  releases to offload the community’s workloads.
* **Make KubeRay thrive:** There are two main indicators that define the vibrancy of the KubeRay community:
  (1) active contributors and (2) user adoptions.
  * **Involve more contributors:** A committer should be able to involve more contributors by providing mentorship or
    reviewing PRs.
  * **Increase user adoptions:** A committer should be able to find user pain points and unlock new use cases by
    delivering new features, help users by answering questions and fixing bugs, author blogs, and give talks.

If contributors commit to the above two areas with long-term contributions, they will be nominated as KubeRay committers.
The following paragraphs provide methods and more details about how to contribute to the two areas, and the order does not
indicate importance.
A contributor doesn't need to contribute to all of them to be a committer, and nothing is strictly required to be a
committer, but all of them will be considered together.

* **Code contribution:**
  * Deliver high-quality code that committers are comfortable merging without a lot of back-and-forth on PR reviews.
  * End-to-end ownership of new features is also important; that is, writing user guides and advertising new features
    after PRs are merged to ensure users can actually use them in production.
* **PR reviews:**
  * Uphold high-quality code reviews, and committers are confident in merging the PR if you approve it.
* **Answer questions:**
  * Answer questions on Slack and GitHub issues to unblock users.
* **Mentorship:**
  * Mentor contributors to be involved in the community.
* **Advocacy:**
  * Improve KubeRay's awareness within the AI infrastructure and cloud-native communities by speaking at conferences and
    writing blogs.
* **KubeRay releases:**
  * Help KubeRay releases ensure high-quality and stable KubeRay releases.
* **Understand user pain points and propose new projects:**
  * Chat with users to understand their pain points and propose new projects or documents to unlock new use cases.
