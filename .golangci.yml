linters-settings:
  gofmt:
    simplify: true
  gosec:
    excludes:
      - G601
  ginkgolinter:
    forbid-focus-container: true
  goimports:
    local-prefixes: github.com/ray-project/kuberay
  misspell:
    locale: US
  nolintlint:
    require-explanation: true
    require-specific: true
  revive:
    ignore-generated-header: true
    rules:
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: empty-block
      - name: error-naming
      - name: error-return
      - name: error-strings
      - name: errorf
      - name: exported
        disabled: true
      - name: if-return
      - name: increment-decrement
      - name: indent-error-flow
      - name: package-comments
      - name: range
      - name: receiver-naming
      - name: redefines-builtin-id
      - name: superfluous-else
      - name: time-naming
      - name: unexported-return
      - name: unreachable-code
      - name: unused-parameter
      - name: var-declaration
      - name: var-naming
        exclude:
          - "**/ray-operator/apis/config/v1alpha1/*.go"
          - "**/ray-operator/apis/ray/v1alpha1/*.go"
          - "**/ray-operator/apis/ray/v1/*.go"
        arguments:
          - ["ID", "JSON", "HTTP", "IP"] # AllowList
          - [] # DenyList
          - - upperCaseConst: true
  gocyclo:
    min-complexity: 15
  govet:
    enable:
      - fieldalignment
  lll:
    line-length: 120
  gci: # Splits all import blocks into different sections and sorts them.
    sections:
      - standard # Go official imports, like "fmt"
      - default # Third-party libraries (anything not in standard or prefix)
      - prefix(github.com/ray-project/kuberay) # kuberay packages
    skip-generated: true
linters:
  enable:
    - asciicheck
    - errcheck
    - errorlint
    - gci
    - ginkgolinter
#    - gocyclo
    - gofmt
    - gofumpt
    - goimports
    - gosec
    - gosimple
    - govet
    - ineffassign
#    - lll
    - makezero
    - misspell
    - nilerr
    - noctx
    - nolintlint
    - predeclared
    - revive
    - staticcheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - wastedassign
    - testifylint
  disable-all: true
issues:
  max-issues-per-linter: 0
  max-same-issues: 0
run:
  timeout: 3m
