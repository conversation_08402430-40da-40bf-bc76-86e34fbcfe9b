{{- if .Values.rbacEnable }}
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-operator.leaderElectionRole.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - get
  - list
  - update
{{- end }}
