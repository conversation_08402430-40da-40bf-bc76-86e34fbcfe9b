{{- if and .Values.rbacEnable .Values.singleNamespaceInstall .Values.crNamespacedRbacEnable }}
{{- $watchNamespaces := default (list .Release.Namespace) .Values.watchNamespace }}
{{- range $namespace := $watchNamespaces }}
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-operator.fullname" $ }}
  namespace: {{ $namespace }}
  labels: {{ include "kuberay-operator.labels" $ | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "kuberay-operator.fullname" $ }}
subjects:
- kind: ServiceAccount
  name: {{ include "kuberay-operator.serviceAccount.name" $ }}
  namespace: {{ $.Release.Namespace }}
{{- end }}
{{- end }}
