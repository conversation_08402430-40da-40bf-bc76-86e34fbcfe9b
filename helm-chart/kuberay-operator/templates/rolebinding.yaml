{{- if and .Values.rbacEnable (not .Values.singleNamespaceInstall) }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-operator.clusterRoleBinding.name" . }}
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
subjects:
- kind: ServiceAccount
  name: {{ include "kuberay-operator.serviceAccount.name" . }}
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "kuberay-operator.clusterRole.name" . }}
{{- end }}
