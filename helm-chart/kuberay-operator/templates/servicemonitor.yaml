{{- if .Values.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "kuberay-operator.fullname" . }}
  namespace: {{ .Values.metrics.serviceMonitor.namespace | default .Release.Namespace }}
  labels:
    {{- with .Values.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  endpoints:
    - path: /metrics
      targetPort: http
      interval: {{ .Values.metrics.serviceMonitor.interval }}
      honorLabels: {{ .Values.metrics.serviceMonitor.honorLabels }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "kuberay-operator.name" . }}
{{- end }}
