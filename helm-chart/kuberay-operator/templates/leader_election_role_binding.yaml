{{- if .Values.rbacEnable -}}
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-operator.leaderElectionRoleBinding.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "kuberay-operator.leaderElectionRole.name" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "kuberay-operator.serviceAccount.name" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
