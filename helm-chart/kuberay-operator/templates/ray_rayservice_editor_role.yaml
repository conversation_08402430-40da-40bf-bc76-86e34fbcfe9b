{{- /* ClusterRole for end users to view and edit RayService. */ -}}
{{- if and .Values.rbacEnable (not .Values.singleNamespaceInstall) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rayservice-editor-role
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ray.io
  resources:
  - rayservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ray.io
  resources:
  - rayservices/status
  verbs:
  - get
{{- end }}
