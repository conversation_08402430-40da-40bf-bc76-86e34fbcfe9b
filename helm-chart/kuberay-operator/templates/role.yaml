{{- if and .Values.rbacEnable (not .Values.singleNamespaceInstall) }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-operator.clusterRole.name" . }}
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
{{ include "role.consistentRules" (dict "batchSchedulerEnabled" .Values.batchScheduler.enabled "batchSchedulerName" .Values.batchScheduler.name) }}
{{- end }}
