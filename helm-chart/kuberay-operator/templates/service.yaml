apiVersion: v1
kind: Service
metadata:
  name: {{ include "kuberay-operator.service.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "kuberay-operator.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
