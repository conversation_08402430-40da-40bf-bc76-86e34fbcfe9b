{{- /* ClusterRole for end users to view RayService. */ -}}
{{- if and .Values.rbacEnable (not .Values.singleNamespaceInstall) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rayservice-viewer-role
  labels:
    {{- include "kuberay-operator.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ray.io
  resources:
  - rayservices
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ray.io
  resources:
  - rayservices/status
  verbs:
  - get
{{- end }}
