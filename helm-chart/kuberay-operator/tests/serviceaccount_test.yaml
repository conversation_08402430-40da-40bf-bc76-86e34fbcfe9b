suite: Test ServiceAccount

templates:
  - serviceaccount.yaml

release:
  name: kuberay-operator
  namespace: kuberay-system

tests:
  - it: Should create ServiceAccount if `serviceAccount.create` is `true`
    set:
      serviceAccount:
        create: true
    asserts:
      - containsDocument:
          apiVersion: v1
          kind: ServiceAccount
          name: kuberay-operator
          namespace: kuberay-system

  - it: Should not create ServiceAccount if `serviceAccount.create` is `false`
    set:
      serviceAccount:
        create: false
    asserts:
      - hasDocuments:
          count: 0
