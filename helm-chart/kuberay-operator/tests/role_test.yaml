suite: Test ClusterRole

templates:
  - role.yaml

release:
  name: kuberay-operator
  namespace: kuberay-system

tests:
  - it: Should not create ClusterRole if `rbacEnable` is `false`
    set:
      rbacEnable: false
    asserts:
      - hasDocuments:
          count: 0

  - it: Should not create ClusterRole if `singleNamespaceInstall` is `true`
    set:
      singleNamespaceInstall: true
    asserts:
      - hasDocuments:
          count: 0

  - it: Should create ClusterRole if `rbacEnable` is `true` and `singleNamespaceInstall` is `false`
    set:
      rbacEnable: true
      singleNamespaceInstall: false
    asserts:
      - containsDocument:
          apiVersion: rbac.authorization.k8s.io/v1
          kind: ClusterRole
          name: kuberay-operator
