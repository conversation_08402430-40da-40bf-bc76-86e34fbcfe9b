# Default values for kuberay-apiserver.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

name: kuberay-apiserver

image:
  repository: quay.io/kuberay/apiserver
  tag: nightly
  pullPolicy: IfNotPresent

# Uncomment allowOrigin to enable CORS
# It'll set the Access-Control-Allow-Origin response header for the HTTP proxy.
cors:
  # allowOrigin: "*"

labels:
  # key1: value1
  # key2: value2

annotations:
  # key1: value1
  # key2: value2

## Install Default RBAC roles and bindings
rbac:
  create: true
  apiVersion: v1

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: kuberay-apiserver

containerPort:
- name: http
  containerPort: 8888
  protocol: TCP
- name: grpc
  containerPort: 8887
  protocol: TCP

resources:
  limits:
    cpu: 500m
    memory: 500Mi
  requests:
    cpu: 300m
    memory: 300Mi

# -- Sidecar containers to run along with the main container.
sidecarContainers: []

# -- Node selector for pods.
nodeSelector: {}

# -- Affinity for pods.
affinity: {}

# -- Tolerations for pods.
tolerations: []

# Only one service type needs to be picked
service:
# ClusterIP service
  type: ClusterIP
  ports:
  - name: http
    protocol: TCP
    port: 8888
    targetPort: 8888
  - name: rpc
    protocol: TCP
    port: 8887
    targetPort: 8887

# You can only enable an ingress or route, if you are using OpenShift cluster
# Also note that in order to enable ingress or route you need to use ClusterIP service

ingress:
  enabled: false
  annotations: {}
  className: ""
  tls: []

route:
  enabled: false
  annotations: {}

rbacEnable: true

# the chart can be installed by users with permissions to a single namespace only
singleNamespaceInstall: false

# If set to true, API server v2 would be served on the same port as the API server v1.
enableAPIServerV2: true

# security definition. Comment it out if security is not required
security:
  proxy:
    repository: quay.io/kuberay/security-proxy
    tag: nightly
    pullPolicy: IfNotPresent
  env:
    HTTP_LOCAL_PORT: 8988
    GRPC_LOCAL_PORT: 8987
    SECURITY_TOKEN: "12345"
    SECURITY_PREFIX: "/"
    ENABLE_GRPC: "true"
