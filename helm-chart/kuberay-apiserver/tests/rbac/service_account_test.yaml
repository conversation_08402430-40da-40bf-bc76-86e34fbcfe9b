suite: Test ServiceAccount

templates:
  - rbac/service_account.yaml

release:
  name: kuberay-apiserver
  namespace: kuberay-system

tests:
  - it: Should not create service account if `serviceAccount.create` is `false`
    set:
      serviceAccount:
        create: false
    asserts:
      - hasDocuments:
          count: 0

  - it: Should create service account if `serviceAccount.create` is `true`
    set:
      serviceAccount:
        create: true
    asserts:
      - containsDocument:
          apiVersion: v1
          kind: ServiceAccount
          name: kuberay-apiserver
          namespace: kuberay-system
