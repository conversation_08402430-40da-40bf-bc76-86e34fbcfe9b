suite: Test Ingress

templates:
  - ingress.yaml

release:
  name: kuberay-apiserver
  namespace: kuberay-system

tests:
  - it: Should not create Ingress if `ingress.enabled` is `false`
    set:
      ingress:
        enabled: false
    asserts:
      - hasDocuments:
          count: 0

  - it: Should create Ingress if `ingress.enabled` is `true`
    capabilities:
      majorVersion: 1
      minorVersion: 19
    set:
      ingress:
        enabled: true
    asserts:
      - containsDocument:
          apiVersion: networking.k8s.io/v1
          kind: Ingress
          name: kuberay-apiserver
          namespace: kuberay-system

  - it: Should create Ingress if `ingress.enabled` is `true`
    capabilities:
      majorVersion: 1
      minorVersion: 14
    set:
      ingress:
        enabled: true
    asserts:
      - containsDocument:
          apiVersion: networking.k8s.io/v1beta1
          kind: Ingress
          name: kuberay-apiserver
          namespace: kuberay-system

  - it: Should create Ingress if `ingress.enabled` is `true`
    capabilities:
      majorVersion: 1
      minorVersion: 12
    set:
      ingress:
        enabled: true
    asserts:
      - containsDocument:
          apiVersion: extensions/v1beta1
          kind: Ingress
          name: kuberay-apiserver
          namespace: kuberay-system
