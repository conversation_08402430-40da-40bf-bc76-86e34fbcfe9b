apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name }}-service
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/component: kuberay-apiserver
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/scrape: "true"
    prometheus.io/port: "8888"
spec:
  type: {{ .Values.service.type }}
  selector:
    app.kubernetes.io/component: {{ include "kuberay-apiserver.name" . }}
    app.kubernetes.io/name: {{ .Release.Name }}
  ports:
    {{- range $port := .Values.service.ports  }}
    - name: {{ $port.name }}
      port: {{ $port.port }}
      {{- if $.Values.security }}
        {{- if eq $port.name "http" }}
      targetPort: {{ $.Values.security.env.HTTP_LOCAL_PORT }}
        {{ else }}
      targetPort: {{ $.Values.security.env.GRPC_LOCAL_PORT }}
        {{ end }}
      {{ else }}
      targetPort: {{ $port.targetPort }}
      {{ end }}
      {{- if $port.nodePort }}
      nodePort: {{ $port.nodePort }}
      {{ end }}
    {{ end }}
