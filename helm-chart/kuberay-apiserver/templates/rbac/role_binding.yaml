{{- if .Values.rbacEnable -}}
{{- if .Values.singleNamespaceInstall -}}
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "kuberay-apiserver.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kuberay-apiserver.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ .Values.name }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.serviceAccount.name }}
  namespace: {{ .Release.Namespace }}
{{- end }}
{{- end }}
