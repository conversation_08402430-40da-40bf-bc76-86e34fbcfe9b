{{- if .Values.rbacEnable }}
{{- if not .Values.singleNamespaceInstall }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.name }}
  labels:
    app.kubernetes.io/name: {{ .Values.name }}
rules:
- apiGroups:
  - ""
  resources:
  - namespaces
  verbs:
  - list
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - list
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - ray.io
  resources:
  - rayclusters
  - rayjobs
  - rayservices
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
{{- end }}
{{- if .Values.enableAPIServerV2 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.name }}-service-access
  labels:
    app.kubernetes.io/name: {{ .Values.name }}
rules:
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - services/proxy
    verbs:
      - get
      - list
      - watch
{{- end }}
{{- end }}
