syntax = "proto3";

option go_package = "github.com/ray-project/kuberay/proto/go_client";
package proto;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  schemes: HTTP;
  responses: {
    key: "default";
    value: {
      schema: {
        json_schema: {
          ref: ".api.Status";
        }
      }
    }
  }
};

service ClusterService {
  // Creates a new Cluster.
  rpc CreateCluster(CreateClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      post: "/apis/v1/namespaces/{namespace}/clusters"
      body: "cluster"
    };
  }

  // Finds a specific Cluster by ID.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/apis/v1/namespaces/{namespace}/clusters/{name}"
    };
  }

  // Finds all Clusters in a given namespace.
  rpc ListCluster(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/apis/v1/namespaces/{namespace}/clusters"
    };
  }

  // Finds all Clusters in all namespaces.
  rpc ListAllClusters(ListAllClustersRequest) returns (ListAllClustersResponse) {
    option (google.api.http) = {
      get: "/apis/v1/clusters"
    };
  }

  // Deletes an cluster without deleting the cluster's runs and jobs. To
  // avoid unexpected behaviors, delete an cluster's runs and jobs before
  // deleting the cluster.
  rpc DeleteCluster(DeleteClusterRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/apis/v1/namespaces/{namespace}/clusters/{name}"
    };
  }
}

message CreateClusterRequest {
  // Required. The cluster to be created.
  Cluster cluster = 1 [(google.api.field_behavior) = REQUIRED];
  // Required. The namespace of the cluster to be created.
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];
}

message GetClusterRequest {
  // Required. The name of the cluster to be retrieved.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
  // Required. The namespace of the cluster to be retrieved.
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];
}

message ListClustersRequest {
  // Required. The namespace of the clusters to be retrieved.
  string namespace = 1 [(google.api.field_behavior) = REQUIRED];
  // A continue token to request the next page of results. The token is acquired
  // from the previous ListCluster call or can be omitted when fetching the first page.
  string continue = 2;
  // The maximum number of clusters to return for the requested page.
  // For backward compatibility, the default value is 0 which returns all clusters without pagination.
  int64 limit = 3;
}

message ListClustersResponse {
  // A list of clusters returned.
  repeated Cluster clusters = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  // The continue token for the next page of clusters.
  string continue = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message ListAllClustersRequest {
  // A continue token to request the next page of results. The token is acquired
  // from the previous ListAllClusters call or can be omitted when fetching the first page.
  string continue = 1;
  // The maximum number of clusters to return per page across all namespaces.
  // For backward compatibility, the default value is 0 which returns all clusters without pagination.
  int64 limit = 2;
}

message ListAllClustersResponse {
  // A list of clusters returned.
  repeated Cluster clusters = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The continue token for the next page of clusters.
  // If there are no more clusters, this field will be empty.
  string continue = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message DeleteClusterRequest {
  // The name of the cluster to be deleted.
  string name = 1 [(google.api.field_behavior) = REQUIRED];
  // The namespace of the cluster to be deleted.
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];
}

message EnvValueFrom {
  // Source of environment variable
  enum Source{
    CONFIGMAP = 0;
    SECRET = 1;
    RESOURCEFIELD = 2;
    FIELD = 3;
  }
  Source source = 1;
  // Name for config map or secret, container name for resource, path for field
  string name = 2;
  // Key for config map or secret, resource name for resource
  string key = 3;
}

// This allows to specify both - environment variables containing values and environment values containing valueFrom
message EnvironmentVariables {
  map<string, string> values = 1;
  map<string, EnvValueFrom> valuesFrom = 2;
}

message AutoscalerOptions {
  // IdleTimeoutSeconds is the number of seconds to wait before scaling down a worker pod which is not using Ray resources.
  // Defaults to 60 (one minute).
  int32 idleTimeoutSeconds = 1;
  // UpscalingMode is "Conservative", "Default", or "Aggressive."
  // Conservative: Upscaling is rate-limited; the number of pending worker pods is at most the size of the Ray cluster.
  // Default: Upscaling is not rate-limited.
  // Aggressive: An alias for Default; upscaling is not rate-limited.
  // It is not read by the KubeRay operator but by the Ray autoscaler.
  string upscalingMode = 2;
  // Image optionally overrides the autoscaler's container image. This override is for provided for autoscaler testing and development.
  string image = 3;
  // ImagePullPolicy optionally overrides the autoscaler container's image pull policy. This override is for provided for autoscaler testing and development.
  string imagePullPolicy = 4;
  // Optional CPUs requirements for autoscaler - default "500m"
  string cpu = 5;
  // Optional memory requirements for autoscaler - default "512Mi"
  string memory = 6;
  // Optional list of environment variables to set in the autoscaler container.
  EnvironmentVariables envs = 7;
  // Optional list of volumeMounts.  This is needed for enabling TLS for the autoscaler container.
  repeated Volume volumes = 8;
}

message Cluster {
  // Required input field. Unique cluster name provided by user.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required input field. Cluster's namespace provided by user
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];

  // Required field. This field indicates the user who owns the cluster.
  string user = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional input field. Ray cluster version
  string version = 4;

  // Optional field.
  enum Environment {
    DEV = 0;
    TESTING = 1;
    STAGING = 2;
    PRODUCTION = 3;
  }
  Environment environment = 5;

  // Required field. This field indicates ray cluster configuration
  ClusterSpec cluster_spec = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Annotations, for example, "kubernetes.io/ingress.class" to define Ingress class
  map<string, string> annotations = 7;

  // Optional input field. Container environment variables from user.
  EnvironmentVariables envs = 8;

  // Output. The time that the cluster created.
  google.protobuf.Timestamp created_at = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output. The time that the cluster deleted.
  google.protobuf.Timestamp deleted_at = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output. The status to show the cluster status.state
  string cluster_state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output. The list related to the cluster.
  repeated ClusterEvent events = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output. The service endpoint of the cluster
  map<string, string> service_endpoint = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Cluster specification.
message ClusterSpec {
  // Required. The head group configuration
  HeadGroupSpec head_group_spec = 1 [(google.api.field_behavior) = REQUIRED];
  // Optional. The worker group configurations
  repeated WorkerGroupSpec worker_group_spec = 2;
  // EnableInTreeAutoscaling indicates whether operator should create in tree autoscaling configs
  bool enableInTreeAutoscaling = 3;
  // AutoscalerOptions specifies optional configuration for the Ray autoscaler.
  AutoscalerOptions autoscalerOptions = 4;
  // Optional. The annotations for the head service
  map<string, string> headServiceAnnotations = 5;
}

message Volume {
  string mount_path = 1;
  enum VolumeType {
    PERSISTENT_VOLUME_CLAIM = 0;
    HOST_PATH = 1;
    EPHEMERAL = 2;    // Ephemeral (https://kubernetes.io/docs/concepts/storage/ephemeral-volumes/#generic-ephemeral-volumes)
    CONFIGMAP = 3;    // Require map name and items, see https://kubernetes.io/docs/concepts/storage/volumes/#configmap
    SECRET    = 4;    // Require secret name and optional items, see https://stackoverflow.com/questions/53296057/how-do-i-mount-a-single-file-from-a-secret-in-kubernetes
    EMPTY_DIR = 5;    // Requires size limit, see https://kubernetes.io/docs/concepts/storage/volumes/#emptydir
  }
  VolumeType volume_type = 2;
  string name = 3;    // volume name
  string source = 4;  // volume source, for example hostpath source, secret or configMap name, etc
  bool read_only = 5; // Read only flag

  // If indicate hostpath, we need to let user indicate which type
  // they would like to use.
  enum HostPathType {
    DIRECTORY = 0;
    FILE = 1;
  }
  HostPathType host_path_type = 6;

  enum MountPropagationMode {
    NONE = 0;
    HOSTTOCONTAINER = 1;
    BIDIRECTIONAL = 2;
  }
  MountPropagationMode mount_propagation_mode = 7;
  // If indicate ephemeral, we need to let user specify volumeClaimTemplate
  string storageClassName = 8;   // If not defined, default is used
  enum AccessMode {
    RWO = 0;    // ReadWriteOnce
    ROX = 1;    // ReadOnlyMany
    RWX = 2;    // ReadWriteMany
  }
  AccessMode accessMode = 9;
  string storage = 10;          // For ephemeral - required storage, GB, for empty dir - MB
  map<string, string> items = 11; // Items used for configMap and secrets
}

// Adds and removes POSIX capabilities from running containers.
message Capabilities {
  // Optional. Added capabilities
  repeated string add = 1;

  // Optional. Removed capabilities
  repeated string drop = 2;
}

// SecurityContext holds security configuration that will be applied to a container.
// Some fields are present in both SecurityContext and PodSecurityContext.  When both
// are set, the values in SecurityContext take precedence.
message SecurityContext {
  // Optional. The capabilities to add/drop when running containers.
  Capabilities capabilities = 1;
  // Optional. Run container in privileged mode - essentially equivalent to root on the host. Default is false.
  optional bool privileged = 2;
}

// Cluster HeadGroup specification
message HeadGroupSpec {
  // Required. The computeTemplate of head node group
  string compute_template = 1 [(google.api.field_behavior) = REQUIRED];
  // Optional field. This field will be used to retrieve right ray container
  string image = 2;
  // Optional. The service type (ClusterIP, NodePort, Load balancer) of the head node
  string service_type = 3;
  // Optional. Enable Ingress
  // if Ingress is enabled, we might have to specify annotation IngressClassAnnotationKey, for the cluster itself, defining Ingress class
  bool enableIngress = 4;
  // Required. The ray start params of head node group.
  map<string, string> ray_start_params = 5 [(google.api.field_behavior) = REQUIRED];
  // Optional. The volumes mount to head pod
  repeated Volume volumes = 6;
  // Optional. ServiceAccount used by head pod
  // Note that the service account has to be created prior to usage here
  string service_account = 7;
  // Optional. image pull secret used by head pod
  string image_pull_secret = 8;
  // Optional. Environment variables for head pod
  EnvironmentVariables environment = 9;
  // Optional. Annotations for the head pod
  map<string, string> annotations = 10;
  // Optional. Labels for the head pod
  map<string, string> labels = 11;
  // Optional image pull policy We only support Always and ifNotPresent
  string imagePullPolicy = 12;
  // Optional. Configure the security context for the head container for debugging etc.
  SecurityContext security_context = 13;
}

message WorkerGroupSpec {
  // Required. Group name of the current worker group
  string group_name = 1 [(google.api.field_behavior) = REQUIRED];
  // Required. The computeTemplate of head node group
  string compute_template = 2 [(google.api.field_behavior) = REQUIRED];
  // Optional field. This field will be used to retrieve right ray container
  string image = 3;
  // Required. Desired replicas of the worker group
  int32 replicas = 4 [(google.api.field_behavior) = REQUIRED];
  // Optional. Min replicas of the worker group, can't be greater than max_replicas.
  int32 min_replicas = 5;
  // Required. Max replicas of the worker group (>0)
  int32 max_replicas = 6 [(google.api.field_behavior) = REQUIRED];
  // Required. The ray start parameters of worker node group
  map<string, string> ray_start_params = 7 [(google.api.field_behavior) = REQUIRED];
  // Optional. The volumes mount to worker pods
  repeated Volume volumes = 8;
  // Optional. ServiceAccount used by worker pod
  // Note that the service account has to be created prior to usage here
  string service_account = 9;
  // Optional. image pull secret used by worker pod
  string image_pull_secret = 10;
  // Optional. Environment variables for worker pod
  EnvironmentVariables environment = 11;
  // Optional. Annotations for the worker pod
  map<string, string> annotations = 12;
  // Optional. Labels for the worker pod
  map<string, string> labels = 13;
  // Optional image pull policy We only support Always and ifNotPresent
  string imagePullPolicy = 14;
  // Optional. Configure the security context for the worker container for debugging etc.
  SecurityContext security_context = 15;
}

message ClusterEvent {
  // Unique Event Id.
  string id = 1;

  // Human readable name for event.
  string name = 2;

  // Event creation time.
  google.protobuf.Timestamp created_at = 3;

  // The first time the event occur.
  google.protobuf.Timestamp first_timestamp = 4;

  // The last time the event occur
  google.protobuf.Timestamp last_timestamp = 5;

  // The reason for the transition into the object's current status.
  string reason = 6;

  // A human-readable description of the status of this operation.
  string message = 7;

  // Type of this event (Normal, Warning), new types could be added in the future
  string type = 8;

  // The number of times this event has occurred.
  int32 count = 9;
}
