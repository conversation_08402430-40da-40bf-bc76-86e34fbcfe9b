// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.17.3
// source: job_submission.proto

package go_client

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SubmitRayJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job to be created
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
	// Required. The job to be created.
	Jobsubmission *RayJobSubmission `protobuf:"bytes,3,opt,name=jobsubmission,proto3" json:"jobsubmission,omitempty"`
}

func (x *SubmitRayJobRequest) Reset() {
	*x = SubmitRayJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRayJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRayJobRequest) ProtoMessage() {}

func (x *SubmitRayJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRayJobRequest.ProtoReflect.Descriptor instead.
func (*SubmitRayJobRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{0}
}

func (x *SubmitRayJobRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *SubmitRayJobRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

func (x *SubmitRayJobRequest) GetJobsubmission() *RayJobSubmission {
	if x != nil {
		return x.Jobsubmission
	}
	return nil
}

type SubmitRayJobReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Created submission ID
	SubmissionId string `protobuf:"bytes,1,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
}

func (x *SubmitRayJobReply) Reset() {
	*x = SubmitRayJobReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitRayJobReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitRayJobReply) ProtoMessage() {}

func (x *SubmitRayJobReply) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitRayJobReply.ProtoReflect.Descriptor instead.
func (*SubmitRayJobReply) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{1}
}

func (x *SubmitRayJobReply) GetSubmissionId() string {
	if x != nil {
		return x.SubmissionId
	}
	return ""
}

type GetJobDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
	// Required. The submission id of the job
	Submissionid string `protobuf:"bytes,3,opt,name=submissionid,proto3" json:"submissionid,omitempty"`
}

func (x *GetJobDetailsRequest) Reset() {
	*x = GetJobDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobDetailsRequest) ProtoMessage() {}

func (x *GetJobDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetJobDetailsRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{2}
}

func (x *GetJobDetailsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GetJobDetailsRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

func (x *GetJobDetailsRequest) GetSubmissionid() string {
	if x != nil {
		return x.Submissionid
	}
	return ""
}

type GetJobLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
	// Required. The submission id of the job
	Submissionid string `protobuf:"bytes,3,opt,name=submissionid,proto3" json:"submissionid,omitempty"`
}

func (x *GetJobLogRequest) Reset() {
	*x = GetJobLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLogRequest) ProtoMessage() {}

func (x *GetJobLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLogRequest.ProtoReflect.Descriptor instead.
func (*GetJobLogRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{3}
}

func (x *GetJobLogRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GetJobLogRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

func (x *GetJobLogRequest) GetSubmissionid() string {
	if x != nil {
		return x.Submissionid
	}
	return ""
}

type GetJobLogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Content of the log. Always from the beginning
	Log string `protobuf:"bytes,1,opt,name=log,proto3" json:"log,omitempty"`
}

func (x *GetJobLogReply) Reset() {
	*x = GetJobLogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobLogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobLogReply) ProtoMessage() {}

func (x *GetJobLogReply) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobLogReply.ProtoReflect.Descriptor instead.
func (*GetJobLogReply) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{4}
}

func (x *GetJobLogReply) GetLog() string {
	if x != nil {
		return x.Log
	}
	return ""
}

type ListJobDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
}

func (x *ListJobDetailsRequest) Reset() {
	*x = ListJobDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobDetailsRequest) ProtoMessage() {}

func (x *ListJobDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobDetailsRequest.ProtoReflect.Descriptor instead.
func (*ListJobDetailsRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{5}
}

func (x *ListJobDetailsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ListJobDetailsRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

type ListJobSubmissionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Submissions []*JobSubmissionInfo `protobuf:"bytes,1,rep,name=submissions,proto3" json:"submissions,omitempty"`
}

func (x *ListJobSubmissionInfo) Reset() {
	*x = ListJobSubmissionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListJobSubmissionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListJobSubmissionInfo) ProtoMessage() {}

func (x *ListJobSubmissionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListJobSubmissionInfo.ProtoReflect.Descriptor instead.
func (*ListJobSubmissionInfo) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{6}
}

func (x *ListJobSubmissionInfo) GetSubmissions() []*JobSubmissionInfo {
	if x != nil {
		return x.Submissions
	}
	return nil
}

type StopRayJobSubmissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
	// Required. The submission id of the job
	Submissionid string `protobuf:"bytes,3,opt,name=submissionid,proto3" json:"submissionid,omitempty"`
}

func (x *StopRayJobSubmissionRequest) Reset() {
	*x = StopRayJobSubmissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopRayJobSubmissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopRayJobSubmissionRequest) ProtoMessage() {}

func (x *StopRayJobSubmissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopRayJobSubmissionRequest.ProtoReflect.Descriptor instead.
func (*StopRayJobSubmissionRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{7}
}

func (x *StopRayJobSubmissionRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *StopRayJobSubmissionRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

func (x *StopRayJobSubmissionRequest) GetSubmissionid() string {
	if x != nil {
		return x.Submissionid
	}
	return ""
}

type DeleteRayJobSubmissionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The namespace of the cluster for the job
	Namespace string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// Required. The name of the cluster for the job
	Clustername string `protobuf:"bytes,2,opt,name=clustername,proto3" json:"clustername,omitempty"`
	// Required. The submission id of the job
	Submissionid string `protobuf:"bytes,3,opt,name=submissionid,proto3" json:"submissionid,omitempty"`
}

func (x *DeleteRayJobSubmissionRequest) Reset() {
	*x = DeleteRayJobSubmissionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRayJobSubmissionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRayJobSubmissionRequest) ProtoMessage() {}

func (x *DeleteRayJobSubmissionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRayJobSubmissionRequest.ProtoReflect.Descriptor instead.
func (*DeleteRayJobSubmissionRequest) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteRayJobSubmissionRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *DeleteRayJobSubmissionRequest) GetClustername() string {
	if x != nil {
		return x.Clustername
	}
	return ""
}

func (x *DeleteRayJobSubmissionRequest) GetSubmissionid() string {
	if x != nil {
		return x.Submissionid
	}
	return ""
}

// RayJobSubmission definition
type RayJobSubmission struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Entry point
	Entrypoint string `protobuf:"bytes,1,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
	// Optional submission id
	SubmissionId string `protobuf:"bytes,2,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
	// Optional. Arbitrary user-provided metadata for the job.
	Metadata map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Optional. The runtime environment for the job.  - yaml string.
	RuntimeEnv string `protobuf:"bytes,4,opt,name=runtime_env,json=runtimeEnv,proto3" json:"runtime_env,omitempty"`
	// Optional. Number of CPUs to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it.
	EntrypointNumCpus float32 `protobuf:"fixed32,5,opt,name=entrypoint_num_cpus,json=entrypointNumCpus,proto3" json:"entrypoint_num_cpus,omitempty"`
	// Optional. Number of GPUs to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it.
	EntrypointNumGpus float32 `protobuf:"fixed32,6,opt,name=entrypoint_num_gpus,json=entrypointNumGpus,proto3" json:"entrypoint_num_gpus,omitempty"`
	// Optional. The quantity of various custom resources to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it.
	EntrypointResources map[string]string `protobuf:"bytes,7,rep,name=entrypoint_resources,json=entrypointResources,proto3" json:"entrypoint_resources,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RayJobSubmission) Reset() {
	*x = RayJobSubmission{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RayJobSubmission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RayJobSubmission) ProtoMessage() {}

func (x *RayJobSubmission) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RayJobSubmission.ProtoReflect.Descriptor instead.
func (*RayJobSubmission) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{9}
}

func (x *RayJobSubmission) GetEntrypoint() string {
	if x != nil {
		return x.Entrypoint
	}
	return ""
}

func (x *RayJobSubmission) GetSubmissionId() string {
	if x != nil {
		return x.SubmissionId
	}
	return ""
}

func (x *RayJobSubmission) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RayJobSubmission) GetRuntimeEnv() string {
	if x != nil {
		return x.RuntimeEnv
	}
	return ""
}

func (x *RayJobSubmission) GetEntrypointNumCpus() float32 {
	if x != nil {
		return x.EntrypointNumCpus
	}
	return 0
}

func (x *RayJobSubmission) GetEntrypointNumGpus() float32 {
	if x != nil {
		return x.EntrypointNumGpus
	}
	return 0
}

func (x *RayJobSubmission) GetEntrypointResources() map[string]string {
	if x != nil {
		return x.EntrypointResources
	}
	return nil
}

type JobSubmissionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Submission entry point
	Entrypoint string `protobuf:"bytes,1,opt,name=entrypoint,proto3" json:"entrypoint,omitempty"`
	// Job ID
	JobId string `protobuf:"bytes,2,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// Submission ID
	SubmissionId string `protobuf:"bytes,3,opt,name=submission_id,json=submissionId,proto3" json:"submission_id,omitempty"`
	// Submission status
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// Associated message
	Message string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	// Error type
	ErrorType string `protobuf:"bytes,6,opt,name=error_type,json=errorType,proto3" json:"error_type,omitempty"`
	// Job Start time
	StartTime uint64 `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// Job end time
	EndTime uint64 `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// Arbitrary user-provided metadata for the job.
	Metadata map[string]string `protobuf:"bytes,9,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The runtime environment for the job
	RuntimeEnv map[string]string `protobuf:"bytes,10,rep,name=runtime_env,json=runtimeEnv,proto3" json:"runtime_env,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *JobSubmissionInfo) Reset() {
	*x = JobSubmissionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_job_submission_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JobSubmissionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobSubmissionInfo) ProtoMessage() {}

func (x *JobSubmissionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_job_submission_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobSubmissionInfo.ProtoReflect.Descriptor instead.
func (*JobSubmissionInfo) Descriptor() ([]byte, []int) {
	return file_job_submission_proto_rawDescGZIP(), []int{10}
}

func (x *JobSubmissionInfo) GetEntrypoint() string {
	if x != nil {
		return x.Entrypoint
	}
	return ""
}

func (x *JobSubmissionInfo) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *JobSubmissionInfo) GetSubmissionId() string {
	if x != nil {
		return x.SubmissionId
	}
	return ""
}

func (x *JobSubmissionInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *JobSubmissionInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JobSubmissionInfo) GetErrorType() string {
	if x != nil {
		return x.ErrorType
	}
	return ""
}

func (x *JobSubmissionInfo) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *JobSubmissionInfo) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *JobSubmissionInfo) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *JobSubmissionInfo) GetRuntimeEnv() map[string]string {
	if x != nil {
		return x.RuntimeEnv
	}
	return nil
}

var File_job_submission_proto protoreflect.FileDescriptor

var file_job_submission_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6a, 0x6f, 0x62, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x01, 0x0a, 0x13, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0d, 0x6a,
	0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x79, 0x4a, 0x6f,
	0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x0d, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x38, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0c,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x69, 0x64, 0x22, 0x85, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62,
	0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a,
	0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x22, 0x22, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x6f,
	0x67, 0x22, 0x61, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a,
	0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x53, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a,
	0x0b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x73, 0x75,
	0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x1b, 0x53, 0x74,
	0x6f, 0x70, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0b,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x22, 0x92, 0x01, 0x0a,
	0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x25, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0c, 0x73, 0x75, 0x62, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69,
	0x64, 0x22, 0x8a, 0x04, 0x0a, 0x10, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x41, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x79, 0x4a, 0x6f,
	0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65,
	0x6e, 0x76, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d,
	0x65, 0x45, 0x6e, 0x76, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x70, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x11, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x43, 0x70, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x5f, 0x67, 0x70, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x11, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x4e, 0x75, 0x6d,
	0x47, 0x70, 0x75, 0x73, 0x12, 0x63, 0x0a, 0x14, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x61, 0x79, 0x4a, 0x6f,
	0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x46, 0x0a, 0x18, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x85,
	0x04, 0x0a, 0x11, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x49, 0x0a, 0x0b, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x18, 0x0a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x6f, 0x62,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52,
	0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x6e, 0x76, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a,
	0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x45, 0x6e, 0x76, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x52, 0x75, 0x6e, 0x74, 0x69,
	0x6d, 0x65, 0x45, 0x6e, 0x76, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xc3, 0x07, 0x0a, 0x17, 0x52, 0x61, 0x79, 0x4a, 0x6f,
	0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x79,
	0x4a, 0x6f, 0x62, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x52, 0x61,
	0x79, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x4d, 0x22, 0x3c, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a,
	0x0d, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x9b,
	0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x12,
	0x4b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d,
	0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x7d, 0x12, 0x94, 0x01, 0x0a,
	0x09, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x47, 0x65, 0x74, 0x4a,
	0x6f, 0x62, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x57, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x51, 0x12, 0x4f, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6c, 0x6f, 0x67, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x64, 0x7d, 0x12, 0x92, 0x01, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4a, 0x6f, 0x62, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x44, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3e, 0x12, 0x3c, 0x2f, 0x61, 0x70, 0x69,
	0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x9d, 0x01, 0x0a, 0x0a, 0x53, 0x74, 0x6f,
	0x70, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x12, 0x22, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x22, 0x4b, 0x2f, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d, 0x2f, 0x6a, 0x6f, 0x62,
	0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x7d, 0x12, 0xa1, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x12, 0x24, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x61, 0x79, 0x4a, 0x6f, 0x62, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x53, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x4d, 0x2a,
	0x4b, 0x2f, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x7d,
	0x2f, 0x6a, 0x6f, 0x62, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x64, 0x7d, 0x42, 0x54, 0x5a, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x61, 0x79, 0x2d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x2f, 0x6b, 0x75, 0x62, 0x65, 0x72, 0x61, 0x79, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x92, 0x41,
	0x21, 0x2a, 0x01, 0x01, 0x52, 0x1c, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0x11, 0x12, 0x0f, 0x0a, 0x0d, 0x1a, 0x0b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_job_submission_proto_rawDescOnce sync.Once
	file_job_submission_proto_rawDescData = file_job_submission_proto_rawDesc
)

func file_job_submission_proto_rawDescGZIP() []byte {
	file_job_submission_proto_rawDescOnce.Do(func() {
		file_job_submission_proto_rawDescData = protoimpl.X.CompressGZIP(file_job_submission_proto_rawDescData)
	})
	return file_job_submission_proto_rawDescData
}

var file_job_submission_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_job_submission_proto_goTypes = []interface{}{
	(*SubmitRayJobRequest)(nil),           // 0: proto.SubmitRayJobRequest
	(*SubmitRayJobReply)(nil),             // 1: proto.SubmitRayJobReply
	(*GetJobDetailsRequest)(nil),          // 2: proto.GetJobDetailsRequest
	(*GetJobLogRequest)(nil),              // 3: proto.GetJobLogRequest
	(*GetJobLogReply)(nil),                // 4: proto.GetJobLogReply
	(*ListJobDetailsRequest)(nil),         // 5: proto.ListJobDetailsRequest
	(*ListJobSubmissionInfo)(nil),         // 6: proto.ListJobSubmissionInfo
	(*StopRayJobSubmissionRequest)(nil),   // 7: proto.StopRayJobSubmissionRequest
	(*DeleteRayJobSubmissionRequest)(nil), // 8: proto.DeleteRayJobSubmissionRequest
	(*RayJobSubmission)(nil),              // 9: proto.RayJobSubmission
	(*JobSubmissionInfo)(nil),             // 10: proto.JobSubmissionInfo
	nil,                                   // 11: proto.RayJobSubmission.MetadataEntry
	nil,                                   // 12: proto.RayJobSubmission.EntrypointResourcesEntry
	nil,                                   // 13: proto.JobSubmissionInfo.MetadataEntry
	nil,                                   // 14: proto.JobSubmissionInfo.RuntimeEnvEntry
	(*emptypb.Empty)(nil),                 // 15: google.protobuf.Empty
}
var file_job_submission_proto_depIdxs = []int32{
	9,  // 0: proto.SubmitRayJobRequest.jobsubmission:type_name -> proto.RayJobSubmission
	10, // 1: proto.ListJobSubmissionInfo.submissions:type_name -> proto.JobSubmissionInfo
	11, // 2: proto.RayJobSubmission.metadata:type_name -> proto.RayJobSubmission.MetadataEntry
	12, // 3: proto.RayJobSubmission.entrypoint_resources:type_name -> proto.RayJobSubmission.EntrypointResourcesEntry
	13, // 4: proto.JobSubmissionInfo.metadata:type_name -> proto.JobSubmissionInfo.MetadataEntry
	14, // 5: proto.JobSubmissionInfo.runtime_env:type_name -> proto.JobSubmissionInfo.RuntimeEnvEntry
	0,  // 6: proto.RayJobSubmissionService.SubmitRayJob:input_type -> proto.SubmitRayJobRequest
	2,  // 7: proto.RayJobSubmissionService.GetJobDetails:input_type -> proto.GetJobDetailsRequest
	3,  // 8: proto.RayJobSubmissionService.GetJobLog:input_type -> proto.GetJobLogRequest
	5,  // 9: proto.RayJobSubmissionService.ListJobDetails:input_type -> proto.ListJobDetailsRequest
	7,  // 10: proto.RayJobSubmissionService.StopRayJob:input_type -> proto.StopRayJobSubmissionRequest
	8,  // 11: proto.RayJobSubmissionService.DeleteRayJob:input_type -> proto.DeleteRayJobSubmissionRequest
	1,  // 12: proto.RayJobSubmissionService.SubmitRayJob:output_type -> proto.SubmitRayJobReply
	10, // 13: proto.RayJobSubmissionService.GetJobDetails:output_type -> proto.JobSubmissionInfo
	4,  // 14: proto.RayJobSubmissionService.GetJobLog:output_type -> proto.GetJobLogReply
	6,  // 15: proto.RayJobSubmissionService.ListJobDetails:output_type -> proto.ListJobSubmissionInfo
	15, // 16: proto.RayJobSubmissionService.StopRayJob:output_type -> google.protobuf.Empty
	15, // 17: proto.RayJobSubmissionService.DeleteRayJob:output_type -> google.protobuf.Empty
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_job_submission_proto_init() }
func file_job_submission_proto_init() {
	if File_job_submission_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_job_submission_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRayJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitRayJobReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobLogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListJobSubmissionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopRayJobSubmissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRayJobSubmissionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RayJobSubmission); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_job_submission_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JobSubmissionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_job_submission_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_job_submission_proto_goTypes,
		DependencyIndexes: file_job_submission_proto_depIdxs,
		MessageInfos:      file_job_submission_proto_msgTypes,
	}.Build()
	File_job_submission_proto = out.File
	file_job_submission_proto_rawDesc = nil
	file_job_submission_proto_goTypes = nil
	file_job_submission_proto_depIdxs = nil
}
