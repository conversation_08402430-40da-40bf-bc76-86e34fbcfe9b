// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package go_client

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RayServeServiceClient is the client API for RayServeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RayServeServiceClient interface {
	// Create a new ray serve.
	CreateRayService(ctx context.Context, in *CreateRayServiceRequest, opts ...grpc.CallOption) (*RayService, error)
	// Update a ray serve.
	// We may
	UpdateRayService(ctx context.Context, in *UpdateRayServiceRequest, opts ...grpc.CallOption) (*RayService, error)
	// Find a specific ray serve by name and namespace.
	GetRayService(ctx context.Context, in *GetRayServiceRequest, opts ...grpc.CallOption) (*RayService, error)
	// Finds all ray services in a given namespace. Supports pagination, and sorting on certain fields.
	ListRayServices(ctx context.Context, in *ListRayServicesRequest, opts ...grpc.CallOption) (*ListRayServicesResponse, error)
	// Finds all ray services in a given namespace. Supports pagination, and sorting on certain fields.
	ListAllRayServices(ctx context.Context, in *ListAllRayServicesRequest, opts ...grpc.CallOption) (*ListAllRayServicesResponse, error)
	// Deletes a ray service by its name and namespace
	DeleteRayService(ctx context.Context, in *DeleteRayServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type rayServeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRayServeServiceClient(cc grpc.ClientConnInterface) RayServeServiceClient {
	return &rayServeServiceClient{cc}
}

func (c *rayServeServiceClient) CreateRayService(ctx context.Context, in *CreateRayServiceRequest, opts ...grpc.CallOption) (*RayService, error) {
	out := new(RayService)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/CreateRayService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rayServeServiceClient) UpdateRayService(ctx context.Context, in *UpdateRayServiceRequest, opts ...grpc.CallOption) (*RayService, error) {
	out := new(RayService)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/UpdateRayService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rayServeServiceClient) GetRayService(ctx context.Context, in *GetRayServiceRequest, opts ...grpc.CallOption) (*RayService, error) {
	out := new(RayService)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/GetRayService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rayServeServiceClient) ListRayServices(ctx context.Context, in *ListRayServicesRequest, opts ...grpc.CallOption) (*ListRayServicesResponse, error) {
	out := new(ListRayServicesResponse)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/ListRayServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rayServeServiceClient) ListAllRayServices(ctx context.Context, in *ListAllRayServicesRequest, opts ...grpc.CallOption) (*ListAllRayServicesResponse, error) {
	out := new(ListAllRayServicesResponse)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/ListAllRayServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rayServeServiceClient) DeleteRayService(ctx context.Context, in *DeleteRayServiceRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/proto.RayServeService/DeleteRayService", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RayServeServiceServer is the server API for RayServeService service.
// All implementations must embed UnimplementedRayServeServiceServer
// for forward compatibility
type RayServeServiceServer interface {
	// Create a new ray serve.
	CreateRayService(context.Context, *CreateRayServiceRequest) (*RayService, error)
	// Update a ray serve.
	// We may
	UpdateRayService(context.Context, *UpdateRayServiceRequest) (*RayService, error)
	// Find a specific ray serve by name and namespace.
	GetRayService(context.Context, *GetRayServiceRequest) (*RayService, error)
	// Finds all ray services in a given namespace. Supports pagination, and sorting on certain fields.
	ListRayServices(context.Context, *ListRayServicesRequest) (*ListRayServicesResponse, error)
	// Finds all ray services in a given namespace. Supports pagination, and sorting on certain fields.
	ListAllRayServices(context.Context, *ListAllRayServicesRequest) (*ListAllRayServicesResponse, error)
	// Deletes a ray service by its name and namespace
	DeleteRayService(context.Context, *DeleteRayServiceRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedRayServeServiceServer()
}

// UnimplementedRayServeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRayServeServiceServer struct {
}

func (UnimplementedRayServeServiceServer) CreateRayService(context.Context, *CreateRayServiceRequest) (*RayService, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRayService not implemented")
}
func (UnimplementedRayServeServiceServer) UpdateRayService(context.Context, *UpdateRayServiceRequest) (*RayService, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRayService not implemented")
}
func (UnimplementedRayServeServiceServer) GetRayService(context.Context, *GetRayServiceRequest) (*RayService, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRayService not implemented")
}
func (UnimplementedRayServeServiceServer) ListRayServices(context.Context, *ListRayServicesRequest) (*ListRayServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRayServices not implemented")
}
func (UnimplementedRayServeServiceServer) ListAllRayServices(context.Context, *ListAllRayServicesRequest) (*ListAllRayServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllRayServices not implemented")
}
func (UnimplementedRayServeServiceServer) DeleteRayService(context.Context, *DeleteRayServiceRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRayService not implemented")
}
func (UnimplementedRayServeServiceServer) mustEmbedUnimplementedRayServeServiceServer() {}

// UnsafeRayServeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RayServeServiceServer will
// result in compilation errors.
type UnsafeRayServeServiceServer interface {
	mustEmbedUnimplementedRayServeServiceServer()
}

func RegisterRayServeServiceServer(s grpc.ServiceRegistrar, srv RayServeServiceServer) {
	s.RegisterService(&RayServeService_ServiceDesc, srv)
}

func _RayServeService_CreateRayService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRayServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).CreateRayService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/CreateRayService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).CreateRayService(ctx, req.(*CreateRayServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RayServeService_UpdateRayService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRayServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).UpdateRayService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/UpdateRayService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).UpdateRayService(ctx, req.(*UpdateRayServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RayServeService_GetRayService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRayServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).GetRayService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/GetRayService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).GetRayService(ctx, req.(*GetRayServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RayServeService_ListRayServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRayServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).ListRayServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/ListRayServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).ListRayServices(ctx, req.(*ListRayServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RayServeService_ListAllRayServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllRayServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).ListAllRayServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/ListAllRayServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).ListAllRayServices(ctx, req.(*ListAllRayServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RayServeService_DeleteRayService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRayServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RayServeServiceServer).DeleteRayService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/proto.RayServeService/DeleteRayService",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RayServeServiceServer).DeleteRayService(ctx, req.(*DeleteRayServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RayServeService_ServiceDesc is the grpc.ServiceDesc for RayServeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RayServeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proto.RayServeService",
	HandlerType: (*RayServeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRayService",
			Handler:    _RayServeService_CreateRayService_Handler,
		},
		{
			MethodName: "UpdateRayService",
			Handler:    _RayServeService_UpdateRayService_Handler,
		},
		{
			MethodName: "GetRayService",
			Handler:    _RayServeService_GetRayService_Handler,
		},
		{
			MethodName: "ListRayServices",
			Handler:    _RayServeService_ListRayServices_Handler,
		},
		{
			MethodName: "ListAllRayServices",
			Handler:    _RayServeService_ListAllRayServices_Handler,
		},
		{
			MethodName: "DeleteRayService",
			Handler:    _RayServeService_DeleteRayService_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "serve.proto",
}
