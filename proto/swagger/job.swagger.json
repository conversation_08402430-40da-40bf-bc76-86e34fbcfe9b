{"swagger": "2.0", "info": {"title": "job.proto", "version": "version not set"}, "tags": [{"name": "RayJobService"}], "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/apis/v1/jobs": {"get": {"summary": "Finds all job in all namespaces. Supports pagination, and sorting on certain fields.", "operationId": "RayJobService_ListAllRayJobs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListAllRayJobsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "continue", "description": "A continue token to request the next page of results. The token is acquired\nfrom the previous ListAllRayJobs call or can be omitted when fetching the first page.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "The maximum number of jobs to return for the requested page.\nFor backward compatibility, the default value is 0 which returns all jobs without pagination.", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["RayJobService"]}}, "/apis/v1/namespaces/{namespace}/jobs": {"get": {"summary": "Finds all job in a given namespace. Supports pagination, and sorting on certain fields.", "operationId": "RayJobService_ListRayJobs", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListRayJobsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the job to be retrieved.", "in": "path", "required": true, "type": "string"}, {"name": "continue", "description": "A continue token to request the next page of results. The token is acquired\nfrom the previous ListRayJobs call or can be omitted when fetching the first page.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "The maximum number of jobs to return for the requested page.\nFor backward compatibility, the default value is 0 which returns all jobs without pagination.", "in": "query", "required": false, "type": "string", "format": "int64"}], "tags": ["RayJobService"]}, "post": {"summary": "Creates a new job.", "operationId": "RayJobService_CreateRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoRayJob"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the job to be created", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": "Required. The job to be created.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/protoRayJob"}}], "tags": ["RayJobService"]}}, "/apis/v1/namespaces/{namespace}/jobs/{name}": {"get": {"summary": "Finds a specific job by its name and namespace.", "operationId": "RayJobService_GetRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoRayJob"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the job to be retrieved.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "Required. The name of the job to be retrieved.", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobService"]}, "delete": {"summary": "Deletes a job by its name and namespace.", "operationId": "RayJobService_DeleteRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the job to be deleted.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "Required. The name of the job to be deleted.", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobService"]}}}, "definitions": {"EnvValueFromSource": {"type": "string", "enum": ["CONFIGMAP", "SECRET", "RESOURCEFIELD", "FIELD"], "default": "CONFIGMAP", "title": "Source of environment variable"}, "VolumeAccessMode": {"type": "string", "enum": ["RWO", "ROX", "RWX"], "default": "RWO"}, "VolumeHostPathType": {"type": "string", "enum": ["DIRECTORY", "FILE"], "default": "DIRECTORY", "description": "If indicate hostpath, we need to let user indicate which type\nthey would like to use."}, "VolumeMountPropagationMode": {"type": "string", "enum": ["NONE", "HOSTTOCONTAINER", "BIDIRECTIONAL"], "default": "NONE"}, "VolumeVolumeType": {"type": "string", "enum": ["PERSISTENT_VOLUME_CLAIM", "HOST_PATH", "EPHEMERAL", "CONFIGMAP", "SECRET", "EMPTY_DIR"], "default": "PERSISTENT_VOLUME_CLAIM"}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "protoAutoscalerOptions": {"type": "object", "properties": {"idleTimeoutSeconds": {"type": "integer", "format": "int32", "description": "IdleTimeoutSeconds is the number of seconds to wait before scaling down a worker pod which is not using Ray resources.\nDefaults to 60 (one minute)."}, "upscalingMode": {"type": "string", "description": "UpscalingMode is \"Conservative\", \"Default\", or \"Aggressive.\"\nConservative: Upscaling is rate-limited; the number of pending worker pods is at most the size of the Ray cluster.\nDefault: Upscaling is not rate-limited.\nAggressive: An alias for <PERSON><PERSON><PERSON>; upscaling is not rate-limited.\nIt is not read by the KubeRay operator but by the Ray autoscaler."}, "image": {"type": "string", "description": "Image optionally overrides the autoscaler's container image. This override is for provided for autoscaler testing and development."}, "imagePullPolicy": {"type": "string", "description": "ImagePullPolicy optionally overrides the autoscaler container's image pull policy. This override is for provided for autoscaler testing and development."}, "cpu": {"type": "string", "title": "Optional CPUs requirements for autoscaler - default \"500m\""}, "memory": {"type": "string", "title": "Optional memory requirements for autoscaler - default \"512Mi\""}, "envs": {"$ref": "#/definitions/protoEnvironmentVariables", "description": "Optional list of environment variables to set in the autoscaler container."}, "volumes": {"type": "array", "items": {"$ref": "#/definitions/protoVolume"}, "description": "Optional list of volumeMounts.  This is needed for enabling TLS for the autoscaler container."}}}, "protoCapabilities": {"type": "object", "properties": {"add": {"type": "array", "items": {"type": "string"}, "title": "Optional. Added capabilities"}, "drop": {"type": "array", "items": {"type": "string"}, "title": "Optional. Removed capabilities"}}, "description": "Adds and removes POSIX capabilities from running containers."}, "protoClusterSpec": {"type": "object", "properties": {"headGroupSpec": {"$ref": "#/definitions/protoHeadGroupSpec", "title": "Required. The head group configuration"}, "workerGroupSpec": {"type": "array", "items": {"$ref": "#/definitions/protoWorkerGroupSpec"}, "title": "Optional. The worker group configurations"}, "enableInTreeAutoscaling": {"type": "boolean", "title": "EnableInTreeAutoscaling indicates whether operator should create in tree autoscaling configs"}, "autoscalerOptions": {"$ref": "#/definitions/protoAutoscalerOptions", "description": "AutoscalerOptions specifies optional configuration for the Ray autoscaler."}, "headServiceAnnotations": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Optional. The annotations for the head service"}}, "description": "Cluster specification."}, "protoEnvValueFrom": {"type": "object", "properties": {"source": {"$ref": "#/definitions/EnvValueFromSource"}, "name": {"type": "string", "title": "Name for config map or secret, container name for resource, path for field"}, "key": {"type": "string", "title": "Key for config map or secret, resource name for resource"}}}, "protoEnvironmentVariables": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "string"}}, "valuesFrom": {"type": "object", "additionalProperties": {"$ref": "#/definitions/protoEnvValueFrom"}}}, "title": "This allows to specify both - environment variables containing values and environment values containing valueFrom"}, "protoHeadGroupSpec": {"type": "object", "properties": {"computeTemplate": {"type": "string", "title": "Required. The computeTemplate of head node group", "required": ["compute_template"]}, "image": {"type": "string", "title": "Optional field. This field will be used to retrieve right ray container"}, "serviceType": {"type": "string", "title": "Optional. The service type (ClusterIP, NodePort, Load balancer) of the head node"}, "enableIngress": {"type": "boolean", "title": "Optional. Enable Ingress\nif Ingress is enabled, we might have to specify annotation IngressClassAnnotationKey, for the cluster itself, defining Ingress class"}, "rayStartParams": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Required. The ray start params of head node group.", "required": ["ray_start_params"]}, "volumes": {"type": "array", "items": {"$ref": "#/definitions/protoVolume"}, "title": "Optional. The volumes mount to head pod"}, "serviceAccount": {"type": "string", "title": "Optional. ServiceAccount used by head pod\nNote that the service account has to be created prior to usage here"}, "imagePullSecret": {"type": "string", "title": "Optional. image pull secret used by head pod"}, "environment": {"$ref": "#/definitions/protoEnvironmentVariables", "title": "Optional. Environment variables for head pod"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Optional. Annotations for the head pod"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Optional. Labels for the head pod"}, "imagePullPolicy": {"type": "string", "title": "Optional image pull policy We only support Always and ifNotPresent"}, "securityContext": {"$ref": "#/definitions/protoSecurityContext", "description": "Optional. Configure the security context for the head container for debugging etc."}}, "title": "Cluster HeadGroup specification", "required": ["computeTemplate", "rayStartParams"]}, "protoListAllRayJobsResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"$ref": "#/definitions/protoRayJob"}, "readOnly": true}, "continue": {"type": "string", "description": "The continue token for the next page of jobs. If it is empty, it means this is the last page.", "readOnly": true}}}, "protoListRayJobsResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"$ref": "#/definitions/protoRayJob"}, "readOnly": true}, "continue": {"type": "string", "description": "The continue token for the next page of jobs. If it is empty, it means this is the last page.", "readOnly": true}}}, "protoRayJob": {"type": "object", "properties": {"name": {"type": "string", "description": "Required input field. Unique job name provided by user.", "required": ["name"]}, "namespace": {"type": "string", "title": "Required input field. job namespace provided by user", "required": ["namespace"]}, "user": {"type": "string", "description": "Required field. This field indicates the user who owns the job.", "required": ["user"]}, "version": {"type": "string", "title": "Required field. This field indicates Ray version. Should be the same as image version", "required": ["version"]}, "entrypoint": {"type": "string", "title": "Required field. The entrypoint of the RayJob", "required": ["entrypoint"]}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Optional. Metadata is data to store along with this job."}, "runtimeEnv": {"type": "string", "title": "Optional. RuntimeEnv is a Yaml string which maps to the RuntimeEnvYAML field of the RayJobSpec"}, "jobId": {"type": "string", "description": "Optional. If jobId is not set, a new jobId will be auto-generated."}, "shutdownAfterJobFinishes": {"type": "boolean", "description": "Optional. If set to true, the rayCluster will be deleted after the rayJob finishes. Defaults to false."}, "clusterSelector": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Optional. The label selectors to choose exiting clusters. If not specified, cluster_spec must be set."}, "clusterSpec": {"$ref": "#/definitions/protoClusterSpec", "description": "Optional. The cluster template, required if the cluster_selector is not specified."}, "ttlSecondsAfterFinished": {"type": "integer", "format": "int32", "description": "Optional. TTLSecondsAfterFinished is the TTL to clean up RayCluster."}, "jobSubmitter": {"$ref": "#/definitions/protoRayJobSubmitter", "title": "Optional Ray Job submitter"}, "entrypointNumCpus": {"type": "number", "format": "float", "description": "Optional entrypointNumCpus specifies the number of cpus to reserve for the entrypoint command."}, "entrypointNumGpus": {"type": "number", "format": "float", "description": "Optional entrypointNumGpus specifies the number of gpus to reserve for the entrypoint command."}, "entrypointResources": {"type": "string", "description": "Optional entrypointResources specifies the custom resources and quantities to reserve\nfor the entrypoint command."}, "activeDeadlineSeconds": {"type": "integer", "format": "int32", "description": "Optional activeDeadlineSeconds is the duration in seconds that the RayJob may be active before\nKubeRay actively tries to terminate the RayJob; value must be positive integer.\nIf not set, the job may run indefinitely until it completes, fails, or is manually stopped."}, "createdAt": {"type": "string", "format": "date-time", "description": "Output. The time that the job created.", "readOnly": true}, "deleteAt": {"type": "string", "format": "date-time", "description": "Output. The time that the job deleted.", "readOnly": true}, "jobStatus": {"type": "string", "title": "Output. The current job status", "readOnly": true}, "jobDeploymentStatus": {"type": "string", "title": "Output. The current job deployment status", "readOnly": true}, "message": {"type": "string", "description": "Output. A human-readable description of the status of this operation.", "readOnly": true}, "startTime": {"type": "string", "format": "date-time", "description": "Output. The time when JobDeploymentStatus transitioned from 'New' to 'Initializing'.", "readOnly": true}, "endTime": {"type": "string", "format": "date-time", "description": "Output. When JobDeploymentStatus transitioned to 'Complete' status.", "readOnly": true}, "rayClusterName": {"type": "string", "description": "Output. Name of the ray cluster.", "readOnly": true}}, "title": "RayJob definition", "required": ["name", "namespace", "user", "version", "entrypoint"]}, "protoRayJobSubmitter": {"type": "object", "properties": {"image": {"type": "string", "title": "Required base image for job submitter. Make sure that Python/Ray version\nof the image corresponds to the one used in the cluster", "required": ["image"]}, "cpu": {"type": "string", "title": "Optional number of CPUs for submitter - default \"1\""}, "memory": {"type": "string", "title": "Optional memory for the submitter - default \"1Gi\""}}, "required": ["image"]}, "protoSecurityContext": {"type": "object", "properties": {"capabilities": {"$ref": "#/definitions/protoCapabilities", "description": "Optional. The capabilities to add/drop when running containers."}, "privileged": {"type": "boolean", "description": "Optional. Run container in privileged mode - essentially equivalent to root on the host. Default is false."}}, "description": "SecurityContext holds security configuration that will be applied to a container.\nSome fields are present in both SecurityContext and PodSecurityContext.  When both\nare set, the values in SecurityContext take precedence."}, "protoVolume": {"type": "object", "properties": {"mountPath": {"type": "string"}, "volumeType": {"$ref": "#/definitions/VolumeVolumeType"}, "name": {"type": "string"}, "source": {"type": "string"}, "readOnly": {"type": "boolean"}, "hostPathType": {"$ref": "#/definitions/VolumeHostPathType"}, "mountPropagationMode": {"$ref": "#/definitions/VolumeMountPropagationMode"}, "storageClassName": {"type": "string", "title": "If indicate ephemeral, we need to let user specify volumeClaimTemplate"}, "accessMode": {"$ref": "#/definitions/VolumeAccessMode"}, "storage": {"type": "string"}, "items": {"type": "object", "additionalProperties": {"type": "string"}}}}, "protoWorkerGroupSpec": {"type": "object", "properties": {"groupName": {"type": "string", "title": "Required. Group name of the current worker group", "required": ["group_name"]}, "computeTemplate": {"type": "string", "title": "Required. The computeTemplate of head node group", "required": ["compute_template"]}, "image": {"type": "string", "title": "Optional field. This field will be used to retrieve right ray container"}, "replicas": {"type": "integer", "format": "int32", "title": "Required. Desired replicas of the worker group", "required": ["replicas"]}, "minReplicas": {"type": "integer", "format": "int32", "description": "Optional. Min replicas of the worker group, can't be greater than max_replicas."}, "maxReplicas": {"type": "integer", "format": "int32", "title": "Required. Max replicas of the worker group (>0)", "required": ["max_replicas"]}, "rayStartParams": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Required. The ray start parameters of worker node group", "required": ["ray_start_params"]}, "volumes": {"type": "array", "items": {"$ref": "#/definitions/protoVolume"}, "title": "Optional. The volumes mount to worker pods"}, "serviceAccount": {"type": "string", "title": "Optional. ServiceAccount used by worker pod\nNote that the service account has to be created prior to usage here"}, "imagePullSecret": {"type": "string", "title": "Optional. image pull secret used by worker pod"}, "environment": {"$ref": "#/definitions/protoEnvironmentVariables", "title": "Optional. Environment variables for worker pod"}, "annotations": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Optional. Annotations for the worker pod"}, "labels": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Optional. Labels for the worker pod"}, "imagePullPolicy": {"type": "string", "title": "Optional image pull policy We only support Always and ifNotPresent"}, "securityContext": {"$ref": "#/definitions/protoSecurityContext", "description": "Optional. Configure the security context for the worker container for debugging etc."}}, "required": ["groupName", "computeTemplate", "replicas", "maxReplicas", "rayStartParams"]}, "protobufAny": {"type": "object", "properties": {"typeUrl": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}, "value": {"type": "string", "format": "byte", "description": "Must be a valid serialized protocol buffer of the above specified type."}}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}}}