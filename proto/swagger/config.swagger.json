{"swagger": "2.0", "info": {"title": "config.proto", "version": "version not set"}, "tags": [{"name": "ComputeTemplateService"}, {"name": "ImageTemplateService"}], "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/apis/v1/compute_templates": {"get": {"summary": "Finds all compute templates in all namespaces.", "operationId": "ComputeTemplateService_ListAllComputeTemplates", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListAllComputeTemplatesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "tags": ["ComputeTemplateService"]}}, "/apis/v1/image_templates": {"post": {"summary": "Not implemented. Creates a new ImageTemplate.", "operationId": "ImageTemplateService_CreateImageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoImageTemplate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "description": "The image template to be created.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/protoImageTemplate"}}, {"name": "namespace", "description": "The namespace of the image template to be created.", "in": "query", "required": false, "type": "string"}], "tags": ["ImageTemplateService"]}}, "/apis/v1/namespaces/{namespace}/compute_templates": {"get": {"summary": "Finds all compute templates in a given namespace.", "operationId": "ComputeTemplateService_ListComputeTemplates", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListComputeTemplatesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the compute templates to be retrieved.", "in": "path", "required": true, "type": "string"}], "tags": ["ComputeTemplateService"]}, "post": {"summary": "Creates a new compute template.", "operationId": "ComputeTemplateService_CreateComputeTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoComputeTemplate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the compute template to be created", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": "The compute template to be created.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/protoComputeTemplate"}}], "tags": ["ComputeTemplateService"]}}, "/apis/v1/namespaces/{namespace}/compute_templates/{name}": {"get": {"summary": "Finds a specific compute template by its name and namespace.", "operationId": "ComputeTemplateService_GetComputeTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoComputeTemplate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the compute template to be retrieved.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "Required. The name of the ComputeTemplate to be retrieved.", "in": "path", "required": true, "type": "string"}], "tags": ["ComputeTemplateService"]}, "delete": {"summary": "Deletes a compute template by its name and namespace", "operationId": "ComputeTemplateService_DeleteComputeTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the compute template to be deleted.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "Required. The name of the compute template to be deleted.", "in": "path", "required": true, "type": "string"}], "tags": ["ComputeTemplateService"]}}, "/apis/v1/namespaces/{namespace}/image_templates": {"get": {"summary": "Not Implemented. Finds all ImageTemplates.", "operationId": "ImageTemplateService_ListImageTemplates", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListImageTemplatesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "The namespace of the image templates to be retrieved.", "in": "path", "required": true, "type": "string"}], "tags": ["ImageTemplateService"]}}, "/apis/v1/namespaces/{namespace}/image_templates/{name}": {"get": {"summary": "Not implemented. Finds a specific ImageTemplate by ID.", "operationId": "ImageTemplateService_GetImageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoImageTemplate"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "The namespace of the image template to be retrieved.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "The name of the image template to be retrieved.", "in": "path", "required": true, "type": "string"}], "tags": ["ImageTemplateService"]}, "delete": {"summary": "Not implemented. Deletes an ImageTemplate.", "operationId": "ImageTemplateService_DeleteImageTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "The namespace of the image template to be deleted.", "in": "path", "required": true, "type": "string"}, {"name": "name", "description": "The name of the image template to be deleted.", "in": "path", "required": true, "type": "string"}], "tags": ["ImageTemplateService"]}}}, "definitions": {"googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "protoComputeTemplate": {"type": "object", "properties": {"name": {"type": "string", "title": "Required. The name of the compute template", "required": ["name"]}, "namespace": {"type": "string", "title": "Required. The namespace of the compute template", "required": ["namespace"]}, "cpu": {"type": "integer", "format": "int64", "title": "Required. Number of cpus", "required": ["cpu"]}, "memory": {"type": "integer", "format": "int64", "title": "Required. Number of memory", "required": ["memory"]}, "gpu": {"type": "integer", "format": "int64", "title": "Optional. Number of gpus"}, "gpuAccelerator": {"type": "string", "title": "Optional. The detail gpu accelerator type"}, "tolerations": {"type": "array", "items": {"$ref": "#/definitions/protoPodToleration"}, "title": "Optional pod tolerations"}, "extendedResources": {"type": "object", "additionalProperties": {"type": "integer", "format": "int64"}, "title": "Optional. Name and number of the extended resources"}}, "title": "ComputeTemplate can be reused by any compute units like worker group, workspace, image build job, etc", "required": ["name", "namespace", "cpu", "memory"]}, "protoImageTemplate": {"type": "object", "properties": {"name": {"type": "string", "title": "The ID of the image template"}, "namespace": {"type": "string", "title": "The namespace of the image template"}, "baseImage": {"type": "string", "title": "The base container image to be used for image building"}, "pipPackages": {"type": "array", "items": {"type": "string"}, "title": "The pip packages to install"}, "condaPackages": {"type": "array", "items": {"type": "string"}, "title": "The conda packages to install"}, "systemPackages": {"type": "array", "items": {"type": "string"}, "title": "The system packages to install"}, "environmentVariables": {"type": "object", "additionalProperties": {"type": "string"}, "title": "The environment variables to set"}, "customCommands": {"type": "string", "title": "The post install commands to execute"}, "image": {"type": "string", "title": "Output. The result image generated"}}, "title": "ImageTemplate can be used by worker group and workspace.\nThey can be distinguish by different entrypoints"}, "protoListAllComputeTemplatesResponse": {"type": "object", "properties": {"computeTemplates": {"type": "array", "items": {"$ref": "#/definitions/protoComputeTemplate"}, "readOnly": true}}}, "protoListComputeTemplatesResponse": {"type": "object", "properties": {"computeTemplates": {"type": "array", "items": {"$ref": "#/definitions/protoComputeTemplate"}, "readOnly": true}}}, "protoListImageTemplatesResponse": {"type": "object", "properties": {"imageTemplates": {"type": "array", "items": {"$ref": "#/definitions/protoImageTemplate"}, "description": "A list of <PERSON><PERSON><PERSON> returned."}}}, "protoPodToleration": {"type": "object", "properties": {"key": {"type": "string", "title": "Required. Key created by the node's taint", "required": ["key"]}, "operator": {"type": "string", "title": "Required. Operator to apply", "required": ["operator"]}, "value": {"type": "string", "title": "optional string"}, "effect": {"type": "string", "title": "Required. Effect", "required": ["effect"]}}, "title": "Toleration for the compute template", "required": ["key", "operator", "effect"]}, "protobufAny": {"type": "object", "properties": {"typeUrl": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}, "value": {"type": "string", "format": "byte", "description": "Must be a valid serialized protocol buffer of the above specified type."}}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}}}