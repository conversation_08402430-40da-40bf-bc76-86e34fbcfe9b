{"swagger": "2.0", "info": {"title": "job_submission.proto", "version": "version not set"}, "tags": [{"name": "RayJobSubmissionService"}], "schemes": ["http"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/apis/v1/namespaces/{namespace}/jobsubmissions/{clustername}": {"get": {"summary": "List all job in a given a given cluster in a namespace. Supports pagination, and sorting on certain fields.", "operationId": "RayJobSubmissionService_ListJobDetails", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoListJobSubmissionInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobSubmissionService"]}, "post": {"summary": "Submit a new Ray job on the specified cluster.", "operationId": "RayJobSubmissionService_SubmitRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoSubmitRayJobReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job to be created", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "body", "description": "Required. The job to be created.", "in": "body", "required": true, "schema": {"$ref": "#/definitions/protoRayJobSubmission"}}], "tags": ["RayJobSubmissionService"]}}, "/apis/v1/namespaces/{namespace}/jobsubmissions/{clustername}/log/{submissionid}": {"get": {"summary": "Gets a specific job log by its submissionid for the cluster with name and namespace.", "operationId": "RayJobSubmissionService_GetJobLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoGetJobLogReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "submissionid", "description": "Required. The submission id of the job", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobSubmissionService"]}}, "/apis/v1/namespaces/{namespace}/jobsubmissions/{clustername}/{submissionid}": {"get": {"summary": "Finds a specific job by its submission_id for the cluster with name and namespace.", "operationId": "RayJobSubmissionService_GetJobDetails", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/protoJobSubmissionInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "submissionid", "description": "Required. The submission id of the job", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobSubmissionService"]}, "delete": {"summary": "Deletes a job by its name and namespace.", "operationId": "RayJobSubmissionService_DeleteRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "submissionid", "description": "Required. The submission id of the job", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobSubmissionService"]}, "post": {"summary": "Stops a job by its name and namespace.", "operationId": "RayJobSubmissionService_StopRayJob", "responses": {"200": {"description": "A successful response.", "schema": {"properties": {}}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "namespace", "description": "Required. The namespace of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "clustername", "description": "Required. The name of the cluster for the job", "in": "path", "required": true, "type": "string"}, {"name": "submissionid", "description": "Required. The submission id of the job", "in": "path", "required": true, "type": "string"}], "tags": ["RayJobSubmissionService"]}}}, "definitions": {"googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "protoGetJobLogReply": {"type": "object", "properties": {"log": {"type": "string", "title": "Content of the log. Always from the beginning"}}}, "protoJobSubmissionInfo": {"type": "object", "properties": {"entrypoint": {"type": "string", "title": "Submission entry point"}, "jobId": {"type": "string", "title": "Job ID"}, "submissionId": {"type": "string", "title": "Submission ID"}, "status": {"type": "string", "title": "Submission status"}, "message": {"type": "string", "title": "Associated message"}, "errorType": {"type": "string", "title": "Error type"}, "startTime": {"type": "string", "format": "uint64", "title": "Job Start time"}, "endTime": {"type": "string", "format": "uint64", "title": "Job end time"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Arbitrary user-provided metadata for the job."}, "runtimeEnv": {"type": "object", "additionalProperties": {"type": "string"}, "title": "The runtime environment for the job"}}}, "protoListJobSubmissionInfo": {"type": "object", "properties": {"submissions": {"type": "array", "items": {"$ref": "#/definitions/protoJobSubmissionInfo"}}}}, "protoRayJobSubmission": {"type": "object", "properties": {"entrypoint": {"type": "string", "title": "Required. Entry point", "required": ["entrypoint"]}, "submissionId": {"type": "string", "title": "Optional submission id"}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Optional. Arbitrary user-provided metadata for the job."}, "runtimeEnv": {"type": "string", "description": "Optional. The runtime environment for the job.  - yaml string."}, "entrypointNumCpus": {"type": "number", "format": "float", "description": "Optional. Number of CPUs to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it."}, "entrypointNumGpus": {"type": "number", "format": "float", "description": "Optional. Number of GPUs to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it."}, "entrypointResources": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Optional. The quantity of various custom resources to allocate for the execution of the entrypoint command, separately from any Ray tasks or actors that are created by it."}}, "title": "RayJobSubmission definition", "required": ["entrypoint"]}, "protoSubmitRayJobReply": {"type": "object", "properties": {"submissionId": {"type": "string", "title": "Created submission ID"}}}, "protobufAny": {"type": "object", "properties": {"typeUrl": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}, "value": {"type": "string", "format": "byte", "description": "Must be a valid serialized protocol buffer of the above specified type."}}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}}}