{"name": "ray-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/joy": "^5.0.0-beta.32", "@mui/material": "^5.15.14", "dayjs": "^1.11.10", "next": "15.2.4", "react": "^18", "react-dom": "^18", "swr": "^2.2.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}