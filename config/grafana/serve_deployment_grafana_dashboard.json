{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "iteration": 1667344411089, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of replicas per deployment. Ignores \"Route\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 0, "w": 8, "h": 8}, "hiddenSeries": false, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_deployment_replica_healthy{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Replicas per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "replicas", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "QPS for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 8, "y": 0, "w": 8, "h": 8}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_deployment_request_counter_total{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Error QPS for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 16, "y": 0, "w": 8, "h": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(ray_serve_deployment_error_counter_total{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error QPS per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "qps", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P50 latency per replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 1, "w": 8, "h": 8}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica, le))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.5, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P50 latency per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P90 latency per replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 8, "y": 1, "w": 8, "h": 8}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica, le))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.9, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P90 latency per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P99 latency per replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 16, "y": 1, "w": 8, "h": 8}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",route!~\"/-/.*\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica, le))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_deployment_processing_latency_ms_bucket{route=~\"$Route\",application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (le))", "interval": "", "legendFormat": "Total", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P99 latency per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Number of requests queued per deployment. Ignores \"Replica\" and \"Route\" variable.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 2, "w": 12, "h": 8}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_deployment_queued_queries{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queue size per deployment", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "requests", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Current running requests for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 12, "y": 2, "w": 12, "h": 8}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_replica_processing_queries{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Running requests per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "requests", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of multiplexed models for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_num_multiplexed_models{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Multiplexed models per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "models", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of times of multiplexed models loaded for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 8, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_multiplexed_models_load_counter_total{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Multiplexed model loads per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "times", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The number of times of multiplexed models unloaded for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 16, "y": 3, "w": 8, "h": 8}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_serve_multiplexed_models_unload_counter_total{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}) by (application, deployment, replica)", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Multiplexed model unloads per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "times", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P99 latency of mutliplexed model load per replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 0, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_multiplexed_model_load_latency_ms_bucket{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica, le))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P99 latency of multiplexed model loads per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "P99 latency of mutliplexed model unload per replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"x": 8, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(ray_serve_multiplexed_model_unload_latency_ms_bucket{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])) by (application, deployment, replica, le))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "P99 latency of multiplexed model unloads per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "ms", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The ids of multiplexed models for each replica.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 16, "y": 4, "w": 8, "h": 8}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "ray_serve_registered_multiplexed_model_id{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}", "interval": "", "legendFormat": "{{replica}}:{{model_id}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Multiplexed model ids per replica", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "model", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "The cache hit rate of multiplexed models for the deployment.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"x": 0, "y": 5, "w": 8, "h": 8}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(1 - sum(rate(ray_serve_multiplexed_models_load_counter_total{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m]))/sum(rate(ray_serve_multiplexed_get_model_requests_counter_total{application=~\"$Application\",deployment=~\"$Deployment\",replica=~\"$Replica\",ray_io_cluster=~\"$Cluster\",}[5m])))", "interval": "", "legendFormat": "{{application}}#{{deployment}}#{{replica}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Multiplexed model cache hit rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "%", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["rayVersion:2.46.0"], "templating": {"list": [{"current": {"selected": false}, "description": "Filter queries to specific prometheus type.", "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_deployment_replica_healthy{}, application)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "Application", "options": [], "query": {"query": "label_values(ray_serve_deployment_replica_healthy{}, application)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_deployment_replica_healthy{application=~\"$Application\",}, deployment)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "Deployment", "options": [], "query": {"query": "label_values(ray_serve_deployment_replica_healthy{application=~\"$Application\",}, deployment)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_deployment_replica_healthy{application=~\"$Application\",deployment=~\"$Deployment\",}, replica)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "Replica", "options": [], "query": {"query": "label_values(ray_serve_deployment_replica_healthy{application=~\"$Application\",deployment=~\"$Deployment\",}, replica)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "${datasource}", "definition": "label_values(ray_serve_deployment_request_counter{deployment=~\"$Deployment\",}, route)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "Route", "options": [], "query": {"query": "label_values(ray_serve_deployment_request_counter{deployment=~\"$Deployment\",}, route)", "refId": "Prometheus-Instance-Variable-Query"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "description": "Filter queries to specific Ray clusters for KubeRay. When ingesting metrics across multiple ray clusters, the ray_io_cluster label should be set per cluster. For KubeRay users, this is done automaticaly with Prometheus PodMonitor.", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "Cluster", "options": [], "query": {"query": "label_values(ray_node_network_receive_speed{}, ray_io_cluster)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "rayMeta": ["excludesSystemRoutes", "supportsGlobalFilterOverride"], "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Serve Deployment Dashboard", "uid": "rayServeDeploymentDashboard", "version": 1}