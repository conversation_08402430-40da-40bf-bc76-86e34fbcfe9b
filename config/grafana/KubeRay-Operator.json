{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 50, "panels": [], "repeat": "Controller", "repeatDirection": "h", "title": "Controller Runtime", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "workqueue_depth{namespace=\"$namespace\", pod=\"$ControllerPod\"}", "instant": false, "legendFormat": "{{controller}}", "range": true, "refId": "A"}], "title": "Workqueue Depth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "The reconciliation rate is calculated by measuring the change in value between consecutive scrape intervals and dividing by the duration of the interval. For example, if the rate is 0.066 and the scrape interval is 30 seconds, it indicates that 2 reconciliations occurred during that interval (2 ÷ 30 = 0.066)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "cps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 51, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "irate(controller_runtime_reconcile_total{namespace=\"$namespace\", pod=\"$ControllerPod\", controller=\"$Controller\"}[$__rate_interval])", "interval": "", "legendFormat": "{{controller}} - {{result}}", "range": true, "refId": "A"}], "title": "Reconciliation Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/^.*\\s-\\smean$/i"}, "properties": [{"id": "custom.fillOpacity", "value": 0}, {"id": "custom.lineStyle", "value": {"dash": [10, 10], "fill": "dash"}}]}, {"matcher": {"id": "byV<PERSON>ue", "options": {"op": "gte", "reducer": "allIsZero", "value": 0}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": true, "tooltip": true, "viz": false}}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 54, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.50, sum(rate(controller_runtime_reconcile_time_seconds_bucket{namespace=\"$namespace\", pod=\"$ControllerPod\", controller=~\"$Controller\"}[5m])) by (pod,le,controller))", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{controller}} - P50", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(controller_runtime_reconcile_time_seconds_bucket{namespace=\"$namespace\", pod=\"$ControllerPod\", controller=~\"$Controller\"}[5m])) by (pod,le,controller))", "interval": "", "intervalFactor": 2, "legendFormat": "{{controller}} - P95", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(controller_runtime_reconcile_time_seconds_bucket{namespace=\"$namespace\", pod=\"$ControllerPod\", controller=~\"$Controller\"}[5m])) by (pod,le,controller))", "interval": "", "intervalFactor": 2, "legendFormat": "{{controller}} - P99", "range": true, "refId": "C"}], "title": "Reconciliation Latency", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 49, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "center", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 18}, "id": 46, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Microservices"}]}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "count(kuberay_cluster_info) by (namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "Count of Existing RayClusters in Namespaces", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true}, "includeByName": {}, "indexByName": {"Time 1": 2, "Time 2": 4, "Time 3": 6, "Value #A": 3, "Value #C": 5, "Value #D": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Time 2": "", "Value": "RayCluster", "Value #A": "Pod", "Value #B": "Configuration", "Value #C": "SVC", "Value #D": "Microservices", "Value #E": "Passwords", "namespace": "Namespace"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Shows the time (in seconds) it took each RayCluster to become provisioned, grouped by name, namespace, and owner type.\n", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 26}, "id": 32, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": ["Value #A"], "reducer": ["mean"], "show": true}, "frameIndex": 0, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_cluster_info{namespace=~\"$namespace\"}", "format": "table", "hide": false, "instant": true, "legendFormat": "", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_cluster_provisioned_duration_seconds{namespace=~\"$namespace\"}", "format": "table", "hide": false, "instant": true, "range": false, "refId": "A"}], "title": "Provisioned Duration ", "transformations": [{"id": "joinByField", "options": {"byField": "name", "mode": "inner"}}, {"id": "organize", "options": {"excludeByName": {"Time 1": true, "Time 2": true, "Value #B": true, "__name__ 1": true, "__name__ 2": true, "container 1": true, "container 2": true, "endpoint 1": true, "endpoint 2": true, "instance 1": true, "instance 2": true, "job 1": true, "job 2": true, "namespace 2": true, "pod 1": true, "pod 2": true, "service 1": true, "service 2": true}, "indexByName": {}, "renameByName": {"Value #A": "seconds"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Displays RayClusters in the selected namespace(s) that have not yet been provisioned (RayClusterProvisioned = false).", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 9, "w": 13, "x": 0, "y": 34}, "id": 55, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 1, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_cluster_condition_provisioned{namespace=~\"$namespace\", condition=~\"false\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "RayClusterProvisioned (false)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "namespace": false, "pod": true, "service": true, "uid": true}, "indexByName": {"Time": 0, "Value": 11, "__name__": 5, "condition": 4, "container": 6, "endpoint": 7, "instance": 8, "job": 9, "name": 2, "namespace": 3, "pod": 1, "service": 10}, "renameByName": {"condition": ""}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Displays RayCluster head pods that are not ready.\n\n", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 9, "w": 11, "x": 13, "y": 34}, "id": 45, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 1, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kube_pod_status_ready{condition=\"false\", namespace=~\"$namespace\"} == 1 and on(pod) kube_pod_info{created_by_kind=\"RayCluster\", pod=~\".*-head\", namespace=~\"$namespace\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (false)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "namespace": false, "pod": false, "service": true, "uid": true}, "indexByName": {"Time": 0, "Value": 10, "__name__": 4, "condition": 3, "container": 5, "endpoint": 6, "instance": 7, "job": 8, "namespace": 2, "pod": 1, "service": 9, "uid": 11}, "renameByName": {"condition": "condition"}}}], "type": "table"}], "title": "RayCluster", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 56, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "center", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 44}, "id": 61, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Microservices"}]}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "count(kuberay_service_info) by (namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "Count of Existing RayClusters in Namespaces", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true}, "includeByName": {}, "indexByName": {"Time 1": 2, "Time 2": 4, "Time 3": 6, "Value #A": 3, "Value #C": 5, "Value #D": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Time 2": "", "Value": "RayService", "Value #A": "Pod", "Value #B": "Configuration", "Value #C": "SVC", "Value #D": "Microservices", "Value #E": "Passwords", "namespace": "Namespace"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Displays RayServices in the selected namespace(s) that have not yet been ready (RayServiceReady = false).", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 13, "x": 0, "y": 52}, "id": 63, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_service_condition_ready{namespace=~\"$namespace\", condition=~\"false\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "RayServiceReady (false)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "namespace": false, "pod": true, "service": true, "uid": true}, "indexByName": {"Time": 0, "Value": 11, "__name__": 5, "condition": 4, "container": 6, "endpoint": 7, "instance": 8, "job": 9, "name": 2, "namespace": 3, "pod": 1, "service": 10}, "renameByName": {"condition": ""}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Displays RayServices in the selected namespace(s) that are currently performing a zero-downtime upgrade (UpgradeInProgress  = false)..", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 9, "w": 11, "x": 13, "y": 52}, "id": 64, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 1, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_service_condition_upgrade_in_progress{namespace=~\"$namespace\", condition=~\"true\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "UpgradeInProgress (true)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "container": true, "endpoint": true, "instance": true, "job": true, "namespace": false, "pod": true, "service": true, "uid": true}, "indexByName": {"Time": 0, "Value": 11, "__name__": 5, "condition": 4, "container": 6, "endpoint": 7, "instance": 8, "job": 9, "name": 2, "namespace": 3, "pod": 1, "service": 10}, "renameByName": {"condition": ""}}}], "type": "table"}], "title": "RayService", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 57, "panels": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "center", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 62}, "id": 58, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Microservices"}]}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "count(kuberay_job_info) by (namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "range": false, "refId": "A"}], "title": "Count of Existing RayJobs in Namespaces", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true}, "includeByName": {}, "indexByName": {"Time 1": 2, "Time 2": 4, "Time 3": 6, "Value #A": 3, "Value #C": 5, "Value #D": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Time 2": "", "Value": "<PERSON><PERSON><PERSON>", "Value #A": "Pod", "Value #B": "Configuration", "Value #C": "SVC", "Value #D": "Microservices", "Value #E": "Passwords", "namespace": "Namespace"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Execution duration of all RayJobs, including those that have been deleted.", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 70}, "id": 60, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": ["Value"], "reducer": ["mean"], "show": true}, "frameIndex": 0, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_job_execution_duration_seconds{namespace=~\"$namespace\"}", "format": "table", "hide": false, "instant": true, "range": false, "refId": "A"}], "title": "Execution Duration ", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Value": false, "Value #A": false, "Value #B": true, "__name__": true, "__name__ 1": true, "__name__ 2": true, "container": true, "container 1": true, "container 2": true, "endpoint": true, "endpoint 1": true, "endpoint 2": true, "instance": true, "instance 1": true, "instance 2": true, "job": true, "job 1": true, "job 2": true, "job_deployment_status": false, "namespace 2": true, "pod": true, "pod 1": true, "pod 2": true, "retry_count": false, "service": true, "service 1": true, "service 2": true}, "indexByName": {"Time": 0, "Value": 12, "__name__": 3, "container": 4, "endpoint": 5, "instance": 6, "job": 7, "job_deployment_status": 8, "name": 1, "namespace": 2, "pod": 9, "retry_count": 10, "service": 11}, "renameByName": {"Value": "seconds", "Value #A": "seconds"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 9, "x": 0, "y": 80}, "id": 67, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "showLegend": true, "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(kuberay_job_deployment_status{namespace=~\"$namespace\"}) by (deployment_status)", "format": "time_series", "instant": true, "interval": "", "legendFormat": "{{deployment_status}}", "range": false, "refId": "A"}], "title": "Deployment Status", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true}, "includeByName": {}, "indexByName": {"Time 1": 2, "Time 2": 4, "Time 3": 6, "Value #A": 3, "Value #C": 5, "Value #D": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Time 2": "", "Value": "<PERSON><PERSON><PERSON>", "Value #A": "Pod", "Value #B": "Configuration", "Value #C": "SVC", "Value #D": "Microservices", "Value #E": "Passwords", "namespace": "Namespace"}}}], "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "\n", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "pod"}, "properties": [{"id": "custom.width", "value": 259}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_name"}, "properties": [{"id": "custom.width", "value": 227}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "__name__ 1"}, "properties": [{"id": "custom.width", "value": 160}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 224}]}]}, "gridPos": {"h": 10, "w": 15, "x": 9, "y": 80}, "id": 68, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "frameIndex": 0, "showHeader": true, "sortBy": []}, "pluginVersion": "10.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "kuberay_job_deployment_status{namespace=~\"$namespace\", deployment_status=~\"Failed|Suspending|Suspended|Retrying|Waiting\"}", "format": "table", "hide": false, "instant": true, "range": false, "refId": "A"}], "title": "Deployment Status (Failed / Suspending / Suspended / Retrying / Waiting)", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Value": true, "Value #A": false, "Value #B": true, "__name__": true, "__name__ 1": true, "__name__ 2": true, "container": true, "container 1": true, "container 2": true, "endpoint": true, "endpoint 1": true, "endpoint 2": true, "instance": true, "instance 1": true, "instance 2": true, "job": true, "job 1": true, "job 2": true, "job_deployment_status": false, "namespace 2": true, "pod": true, "pod 1": true, "pod 2": true, "retry_count": false, "service": true, "service 1": true, "service 2": true}, "indexByName": {"Time": 0, "Value": 11, "__name__": 4, "container": 5, "deployment_status": 3, "endpoint": 6, "instance": 7, "job": 8, "name": 1, "namespace": 2, "pod": 9, "service": 10}, "renameByName": {"Value": "", "Value #A": "seconds"}}}], "type": "table"}], "title": "<PERSON><PERSON><PERSON>", "type": "row"}], "refresh": "10s", "schemaVersion": 38, "style": "dark", "tags": ["Prometheus", "KubeRay Operator", "kube-state-metrics"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": true, "text": ["default"], "value": ["default"]}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(kube_pod_info{},namespace)", "hide": 0, "includeAll": true, "multi": true, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_info{},namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "raycluster", "value": "raycluster"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(controller_runtime_active_workers{namespace=\"$namespace\", pod=\"$ControllerPod\"},controller)", "hide": 0, "includeAll": false, "multi": false, "name": "Controller", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{namespace=\"$namespace\", pod=\"$ControllerPod\"},controller)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "kuberay-operator-6c6dd95c6b-vqfgg", "value": "kuberay-operator-6c6dd95c6b-vqfgg"}, "datasource": {"type": "prometheus", "uid": "prometheus"}, "definition": "label_values(controller_runtime_active_workers{namespace=\"$namespace\"},pod)", "hide": 0, "includeAll": false, "multi": false, "name": "ControllerPod", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{namespace=\"$namespace\"},pod)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "KubeRay Operator", "uid": "kuberay_operator", "version": 1, "weekStart": ""}