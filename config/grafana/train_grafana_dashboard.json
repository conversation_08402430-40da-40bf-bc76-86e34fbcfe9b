{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "iteration": 1667344411089, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Time taken to report a checkpoint to storage.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_train_report_total_blocked_time_s{SessionName=~\"$SessionName\",ray_train_run_name=~\"$TrainRunName\",}) by (ray_train_run_name, ray_train_worker_world_rank)", "interval": "", "legendFormat": "Run Name: {{ray_train_run_name}}, World Rank: {{ray_train_worker_world_rank}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Checkpoint Report Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Time taken by the controller to perform various operations.", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.17", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:2987", "alias": "MAX", "dashes": true, "color": "#1F60C4", "fill": 0, "stack": false}, {"$$hashKey": "object:78", "alias": "/FINISHED|FAILED|DEAD|REMOVED|Failed Nodes:/", "hiddenSeries": true}, {"$$hashKey": "object:2987", "alias": "MAX + PENDING", "dashes": true, "color": "#777777", "fill": 0, "stack": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ray_train_worker_group_start_total_time_s{SessionName=~\"$SessionName\",ray_train_run_name=~\"$TrainRunName\",}) by (ray_train_run_name)", "interval": "", "legendFormat": "Run Name: {{ray_train_run_name}}, Worker Group Start Time", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(ray_train_worker_group_shutdown_total_time_s{SessionName=~\"$SessionName\",ray_train_run_name=~\"$TrainRunName\",}) by (ray_train_run_name)", "interval": "", "legendFormat": "Run Name: {{ray_train_run_name}}, Worker Group Shutdown Time", "queryType": "randomWalk", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Train Controller Operation Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:628", "format": "seconds", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:629", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["rayVersion:2.46.0"], "templating": {"list": [{"current": {"selected": false}, "description": "Filter queries of a specific Prometheus type.", "hide": 2, "includeAll": false, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".+", "current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_train_report_total_blocked_time_s{}, SessionName)", "description": "Filter queries to specific ray sessions.", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "SessionName", "options": [], "query": {"query": "label_values(ray_train_report_total_blocked_time_s{}, SessionName)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".+", "current": {"selected": false}, "datasource": "${datasource}", "definition": "label_values(ray_train_report_total_blocked_time_s{}, ray_train_run_name)", "description": "Filter queries to specific ray sessions.", "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "TrainRunName", "options": [], "query": {"query": "label_values(ray_train_report_total_blocked_time_s{}, ray_train_run_name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Train Dashboard", "uid": "rayTrainDashboard", "version": 1, "rayMeta": ["supportsGlobalFilterOverride"]}