{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "KubeRay-ApiServer service monitoring", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 14765, "graphTooltip": 0, "id": 26, "iteration": 1650105328668, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"0": {"text": ":("}, "1": {"text": ":)"}}, "type": "value"}, {"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "id": 15, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.4.5", "targets": [{"expr": "sum(up{job=\"$job\"})", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Up", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.4.5", "targets": [{"exemplar": true, "expr": "1 - ((sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Internal\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unknown\"}[$interval])) +\nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unavailable\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unimplemented\"}[$interval]))\n) / sum(rate(grpc_server_started_total{job=\"$job\",grpc_type=\"unary\"}[$interval])))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Success", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "id": 14, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.4.5", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_started_total{job=\"$job\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "RPS", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"index": 0, "text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 75, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.4.5", "targets": [{"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "exemplar": true, "expr": "histogram_quantile(0.95, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "95%", "refId": "A"}], "title": "Latency", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 72, "panels": [], "title": "Go Stats", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 6}, "id": 32, "links": [], "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "avg(go_goroutines{job=\"$job\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "go_goroutines", "refId": "A", "step": 4}], "title": "Goroutines", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 6}, "id": 30, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "avg(go_gc_duration_seconds{job=\"$job\"}) by (quantile)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{quantile}}", "metric": "go_gc_duration_seconds", "refId": "A", "step": 4}], "title": "GC duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "alloc rate"}, "properties": [{"id": "unit", "value": "Bps"}]}]}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 6}, "id": 34, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "avg(go_memstats_alloc_bytes{job=\"$job\"})", "interval": "", "intervalFactor": 2, "legendFormat": "bytes allocated", "metric": "go_memstats_alloc_bytes", "refId": "A", "step": 4}, {"exemplar": true, "expr": "avg(rate(go_memstats_alloc_bytes_total{job=\"$job\"}[$interval]))", "interval": "", "intervalFactor": 2, "legendFormat": "alloc rate", "metric": "go_memstats_alloc_bytes_total", "refId": "B", "step": 4}, {"exemplar": true, "expr": "avg(go_memstats_stack_inuse_bytes{job=\"$job\"})", "interval": "", "intervalFactor": 2, "legendFormat": "stack inuse", "metric": "go_memstats_stack_inuse_bytes", "refId": "C", "step": 4}, {"exemplar": true, "expr": "avg(go_memstats_heap_inuse_bytes{job=\"$job\"})", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "heap inuse", "metric": "go_memstats_heap_inuse_bytes", "refId": "D", "step": 4}], "title": "Memory", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 74, "panels": [], "title": "gRPC Stats", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "id": 76, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"OK\"}[$interval]))\n/ sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "OK", "refId": "A"}, {"exemplar": true, "expr": "(sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"InvalidArgument\"}[$interval])) +\nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"NotFound\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"AlreadyExists\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unauthenticated\"}[$interval])) +\nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"PermissionDenied\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"FailedPrecondition\"}[$interval]))\n)/ sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\"}[$interval]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "ClientError", "refId": "B"}, {"exemplar": true, "expr": "(sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Internal\"}[$interval])) +\nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unknown\"}[$interval])) + \nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unavailable\"}[$interval])) +\nsum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\",grpc_code=\"Unimplemented\"}[$interval]))\n)/ sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\"}[$interval]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "ServerError", "refId": "C"}], "title": "Status", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "id": 26, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (grpc_code)\n/ ignoring(grpc_code) group_left sum(rate(grpc_server_handled_total{job=\"$job\",grpc_type=\"unary\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{grpc_code}}", "refId": "A"}], "title": "Status distribution", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "id": 2, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_started_total{job=\"$job\"}[$interval])) by (grpc_service)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{grpc_service}}", "refId": "A"}], "title": "RPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "id": 78, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_started_total{job=\"$job\"}[$interval])) by (grpc_service) \n/ ignoring(grpc_service) group_left sum(rate(grpc_server_started_total{job=\"$job\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{grpc_service}}", "refId": "A"}], "title": "Request distribution", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 77, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.95, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (grpc_service,le)\n)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{grpc_service}}", "refId": "A"}], "title": "Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "id": 16, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "99%", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.95, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "95%", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.90, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "90%", "refId": "E"}, {"exemplar": true, "expr": "histogram_quantile(0.75, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "75%", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.50, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "50%", "refId": "D"}, {"exemplar": true, "expr": "histogram_quantile(0.25, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "25%", "refId": "F"}, {"exemplar": true, "expr": "histogram_quantile(0.10, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"$job\",grpc_type=\"unary\"}[$interval])) by (le)\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "10%", "refId": "G"}], "title": "Latency distribution", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 80, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_msg_sent_total{job=\"$job\",grpc_type!=\"unary\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "sent", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(grpc_server_msg_received_total{job=\"$job\",grpc_type!=\"unary\"}[$interval]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "received", "refId": "B"}], "title": "RPC stream ops", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "points", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 79, "links": [], "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"exemplar": true, "expr": "sum(rate(grpc_server_handled_total{job=\"$job\"}[$interval])) by (grpc_type)\n/ ignoring(grpc_type) group_left sum(rate(grpc_server_handled_total{job=\"$job\"}[$interval]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{grpc_type}}", "refId": "A"}], "title": "RPC type distribution", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 35, "style": "dark", "tags": ["KubeRay"], "templating": {"list": [{"current": {"selected": false, "text": "alertmanager-main", "value": "alertmanager-main"}, "datasource": {"type": "prometheus", "uid": "P1809F7CD0C75ACF3"}, "definition": "", "hide": 0, "includeAll": false, "multi": false, "name": "job", "options": [], "query": {"query": "label_values(go_memstats_alloc_bytes,job)", "refId": "Prometheus-job-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "1m", "value": "1m"}, "hide": 0, "name": "interval", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "15m", "value": "15m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}], "query": "1m,5m,10m,15m,30m,1h", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"hidden": false, "refresh_intervals": ["10s", "15s", "30s", "1m", "5m", "10m", "15m"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "KubeRay-ApiServer", "uid": "gn38yKZnk", "version": 3, "weekStart": ""}