[metadata]
name = python_client
version = 1.1.0
author = <PERSON>
description = A Kuberay python client library to create/delete/update clusters
long_description = file: README.md
keywords = kuberay, ray, python-kuberay
license = Apache License 2.0
url = https://github.com/ray-project/kuberay/
classifiers =
    Programming Language :: Python :: 3

[options]
packages = find:
python_requires = >=3.6.5
install_requires = kubernetes

[options.packages.find]
exclude =
    examples*
    tools*
    docs*
    python_client_test.tests*
