apiVersion: apps/v1
kind: Deployment
metadata:
  name: kuberay-apiserver
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: kuberay
      app.kubernetes.io/component: kuberay-apiserver
  replicas: 1
  template:
    metadata:
      labels:
        app.kubernetes.io/name: kuberay
        app.kubernetes.io/component: kuberay-apiserver
    spec:
      serviceAccountName: kuberay-apiserver
      containers:
      - name: kuberay-apiserver
        image: kuberay/apiserver
        ports:
        - name: http
          containerPort: 8888
          protocol: TCP
        - name: grpc
          containerPort: 8887
          protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 300m
            memory: 300Mi
        livenessProbe:
          httpGet:
            path: /healthz
            port: http
        readinessProbe:
          httpGet:
            path: /healthz
            port: http
---
apiVersion: v1
kind: Service
metadata:
  name: kuberay-apiserver
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/scrape: "true"
    prometheus.io/port: "8888"
  labels:
    app.kubernetes.io/component: kuberay-apiserver

spec:
  type: NodePort
  selector:
    app.kubernetes.io/name: kuberay
    app.kubernetes.io/component: kuberay-apiserver
  ports:
    - name: http
      port: 8888
      targetPort: 8888
      nodePort: 31888
    - name: rpc
      port: 8887
      targetPort: 8887
      nodePort: 31887

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kuberay-apiserver

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kuberay-apiserver
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kuberay-apiserver
subjects:
  - kind: ServiceAccount
    name: kuberay-apiserver
    namespace: ray-system

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kuberay-apiserver
rules:
- apiGroups:
  - ray.io
  resources:
  - rayclusters
  - rayjobs
  - rayservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - namespaces
  verbs:
  - list
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - get
  - list
---
# apiserversdk requires the following permissions to be able to list and proxy services
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: allow-service-access
rules:
  - apiGroups: [""]
    resources: ["services"]
    verbs: ["get", "watch", "list"]
  - apiGroups: [""]
    resources: ["services/proxy"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: allow-service-access
subjects:
  - kind: ServiceAccount
    name: kuberay-apiserver
    namespace: ray-system
roleRef:
  kind: ClusterRole
  name: allow-service-access
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: Namespace
metadata:
  name: ray-system
