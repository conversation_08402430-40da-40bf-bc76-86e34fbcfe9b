apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ray-system

resources:
- apiserver.yaml

images:
- name: kuberay/apiserver
  newName: kuberay/apiserver
  newTag: nightly
- name: kuberay/security-proxy
  newName: kuberay/security-proxy
  newTag: nightly
labels:
- includeSelectors: true
  pairs:
    app.kubernetes.io/component: kuberay-apiserver
    app.kubernetes.io/name: kuberay
