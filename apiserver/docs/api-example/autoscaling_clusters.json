{"name": "test-cluster", "namespace": "default", "user": "<PERSON><PERSON><PERSON>", "clusterSpec": {"enableInTreeAutoscaling": true, "autoscalerOptions": {"upscalingMode": "<PERSON><PERSON><PERSON>", "idleTimeoutSeconds": 30, "cpu": "500m", "memory": "512Mi"}, "headGroupSpec": {"computeTemplate": "default-template", "image": "rayproject/ray:2.46.0-py310", "rayStartParams": {"num-cpus": "0"}, "volumes": [{"name": "code-sample", "mountPath": "/home/<USER>/samples", "volumeType": "CONFIGMAP", "source": "ray-example", "items": {"detached_actor.py": "detached_actor.py", "terminate_detached_actor.py": "terminate_detached_actor.py"}}]}, "workerGroupSpec": [{"groupName": "small-wg", "computeTemplate": "default-template", "image": "rayproject/ray:2.46.0-py310", "replicas": 0, "minReplicas": 0, "maxReplicas": 5}]}}