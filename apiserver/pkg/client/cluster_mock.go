// Code generated by MockGen. DO NOT EDIT.
// Source: cluster.go

// Package client is a generated GoMock package.
package client

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/typed/ray/v1"
)

// MockClusterClientInterface is a mock of ClusterClientInterface interface.
type MockClusterClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockClusterClientInterfaceMockRecorder
}

// MockClusterClientInterfaceMockRecorder is the mock recorder for MockClusterClientInterface.
type MockClusterClientInterfaceMockRecorder struct {
	mock *MockClusterClientInterface
}

// NewMockClusterClientInterface creates a new mock instance.
func NewMockClusterClientInterface(ctrl *gomock.Controller) *MockClusterClientInterface {
	mock := &MockClusterClientInterface{ctrl: ctrl}
	mock.recorder = &MockClusterClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClusterClientInterface) EXPECT() *MockClusterClientInterfaceMockRecorder {
	return m.recorder
}

// RayClusterClient mocks base method.
func (m *MockClusterClientInterface) RayClusterClient(namespace string) v1.RayClusterInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RayClusterClient", namespace)
	ret0, _ := ret[0].(v1.RayClusterInterface)
	return ret0
}

// RayClusterClient indicates an expected call of RayClusterClient.
func (mr *MockClusterClientInterfaceMockRecorder) RayClusterClient(namespace interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RayClusterClient", reflect.TypeOf((*MockClusterClientInterface)(nil).RayClusterClient), namespace)
}
