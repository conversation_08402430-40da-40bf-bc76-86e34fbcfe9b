# Ignore backup files.
*~
# Ignore Vim swap files.
**/.*.swp
# Ignore files generated by IDEs.
**/.classpath
**/.factorypath
**/.idea/
**/.ijwb/
**/.project
**/.settings
**/.vscode/
**/bazel.iml
**/.DS_Store
# Ignore all bazel-* symlinks. There is no full list since this can change
# based on the name of the directory bazel is cloned into.
**/bazel-*
# Ignore outputs generated during Bazel bootstrapping.
**/output/
# Ignore jekyll build output.
**/production
**/.sass-cache
# Bazelisk version file
**/.bazelversion
# User-specific .bazelrc
**/user.bazelrc

# Binaries for programs and plugins
**/*.exe
**/*.exe~
**/*.dll
**/*.so
**/*.dylib
**/testbin/*

# Test binary, built with `go test -c`
**/*.test

# Output of the go coverage tool, specifically when used with LiteIDE
**/*.out

**/bin/
# Dependency directories (remove the comment below to include it)
**/vendor/

.ipynb_checkpoints

# Any file with a .backup extension
**/*.backup

# Any file with a .log extension
**/*.log

# Ignore generated CRD schema files
schema/
