version: 2

before:
  hooks:
    - cp ../LICENSE .
    - go mod tidy

builds:
  - env:
      - CGO_ENABLED=0
    goos:
      - linux
      - darwin
    goarch:
      - amd64
      - arm64
    main: ./cmd
    binary: kubectl-ray
    ldflags: -X github.com/ray-project/kuberay/kubectl-plugin/pkg/cmd/version.Version={{- .Tag -}}

archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of `uname`.
    name_template: >-
      {{ .Binary }}_
      {{- .Tag }}_
      {{- .Os }}_
      {{- if eq .Arch "amd64" }}amd64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        format: zip

changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
