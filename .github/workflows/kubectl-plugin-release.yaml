name: release-kubectl-plugin
on:
  workflow_dispatch:

jobs:
  release-kubectl-plugin:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Error if not a tag
        uses: actions/github-script@v7
        if: ${{ ! startsWith(github.ref, 'refs/tags/') }}
        with:
          script: core.setFailed('This action can only be run on tags')
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24'
      - name: GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          distribution: 'goreleaser'
          version: latest
          args: release --clean
          workdir: 'kubectl-plugin'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Update new version in krew-index
        uses: rajatjindal/krew-release-bot@v0.0.46
