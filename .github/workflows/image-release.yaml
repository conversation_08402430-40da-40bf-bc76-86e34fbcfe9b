name: release-image-build

on:
  workflow_dispatch:

jobs:
  release_apiserver_image:
    env:
      working-directory: ./apiserver
    name: Release APIServer Docker Image
    runs-on: ubuntu-22.04
    steps:

    - name: Error if not a tag
      uses: actions/github-script@v7
      if: ${{ ! startsWith(github.ref, 'refs/tags/') }}
      with:
        script: core.setFailed('This action can only be run on tags')

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: v1.24

    - name: Check out code into the Go module directory
      uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - name: Extract tag
      id: tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

    - name: Install kubebuilder
      run: |
        wget https://github.com/kubernetes-sigs/kubebuilder/releases/download/v3.0.0/kubebuilder_$(go env GOOS)_$(go env GOARCH)
        sudo mv kubebuilder_$(go env GOOS)_$(go env GOARCH) /usr/local/bin/kubebuilder

    - name: Get revision SHA
      id: vars
      run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Get dependencies
      run: go mod download
      working-directory: ${{env.working-directory}}

    - name: Build
      run: go build ./...
      working-directory: ${{env.working-directory}}

    - name: Test
      run: go test ./pkg/... ./cmd/... -race -parallel 4
      working-directory: ${{env.working-directory}}

    - name: Set up Docker
      uses: docker/setup-docker-action@v4

    - name: Build Docker Image - Apiserver
      run: |
        docker build -t kuberay/apiserver:${{ steps.vars.outputs.sha_short }} -f apiserver/Dockerfile .
        docker save -o /tmp/apiserver.tar kuberay/apiserver:${{ steps.vars.outputs.sha_short }}

    - name: Log in to Quay.io
      uses: docker/login-action@v2
      with:
        registry: quay.io
        username: ${{ secrets.QUAY_USERNAME }}
        password: ${{ secrets.QUAY_ROBOT_TOKEN }}

    - name: Push Apiserver to Quay.io
      run: |
        docker image tag kuberay/apiserver:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/apiserver:${{ steps.vars.outputs.sha_short }};
        docker push quay.io/kuberay/apiserver:${{ steps.vars.outputs.sha_short }};
        docker image tag kuberay/apiserver:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/apiserver:${{ env.tag }};
        docker push quay.io/kuberay/apiserver:${{ env.tag }}

  release_operator_image:
    env:
      working-directory: ./ray-operator
    name: Release Operator Docker Images
    runs-on: ubuntu-22.04
    steps:

    - name: Error if not a tag
      uses: actions/github-script@v7
      if: ${{ ! startsWith(github.ref, 'refs/tags/') }}
      with:
        script: core.setFailed('This action can only be run on tags')

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: v1.24

    - name: Check out code into the Go module directory
      uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - name: Extract tag
      id: tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

    - name: Install kubebuilder
      run: |
        wget https://github.com/kubernetes-sigs/kubebuilder/releases/download/v3.0.0/kubebuilder_$(go env GOOS)_$(go env GOARCH)
        sudo mv kubebuilder_$(go env GOOS)_$(go env GOARCH) /usr/local/bin/kubebuilder

    - name: Get revision SHA
      id: vars
      run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Get dependencies
      run: go mod download
      working-directory: ${{env.working-directory}}

    - name: Build
      run: make build
      working-directory: ${{env.working-directory}}

    - name: Test
      run: make test
      working-directory: ${{env.working-directory}}

    - name: Set up Docker
      uses: docker/setup-docker-action@v4

    - name: Build Docker Image - Operator
      run: |
        IMG=kuberay/operator:${{ steps.vars.outputs.sha_short }} make docker-image
      working-directory: ${{env.working-directory}}

    - name: Log in to Quay.io
      uses: docker/login-action@v2
      with:
        registry: quay.io
        username: ${{ secrets.QUAY_USERNAME }}
        password: ${{ secrets.QUAY_ROBOT_TOKEN }}

    # Build operators inside the gh runner vm directly and then copy the go binaries to docker images using the Dockerfile.buildx
    - name: Build linux/amd64 Operator go binary
      env:
        CGO_ENABLED: 1
        GOOS: linux
        GOARCH: amd64
      run: |
        CGO_ENABLED=$CGO_ENABLED GOOS=$GOOS GOARCH=$GOARCH go build -tags strictfipsruntime -a -o manager-$GOARCH main.go
      working-directory: ${{env.working-directory}}

    - name: Build linux/arm64 Operator binary
      env:
        CC: aarch64-linux-gnu-gcc
        CGO_ENABLED: 1
        GOOS: linux
        GOARCH: arm64
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-aarch64-linux-gnu libc6-dev-arm64-cross
        CC=$CC CGO_ENABLED=$CGO_ENABLED GOOS=$GOOS GOARCH=$GOARCH go build -tags strictfipsruntime -a -o manager-$GOARCH main.go
      working-directory: ${{env.working-directory}}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build MultiArch Image
      uses: docker/build-push-action@v5
      env:
        PUSH: true
        REPO_ORG: kuberay
        REPO_NAME: operator
      with:
        platforms: linux/amd64,linux/arm64
        context: ${{env.working-directory}}
        file: ${{env.working-directory}}/Dockerfile.buildx
        push: ${{env.PUSH}}
        provenance: false
        tags: |
          quay.io/${{env.REPO_ORG}}/${{env.REPO_NAME}}:${{ steps.vars.outputs.sha_short }}
          quay.io/${{env.REPO_ORG}}/${{env.REPO_NAME}}:${{ env.tag }}

    - name: Create ray-operator tag
      uses: actions/github-script@v6
      with:
        script: |
          await github.rest.git.createRef({
            owner: context.repo.owner,
            repo: context.repo.repo,
            ref: 'refs/tags/ray-operator/${{ env.tag }}',
            sha: '${{ github.sha }}'
          })

  release_dashboard_image:
    env:
      working-directory: ./dashboard
    name: Release Dashboard Docker Image
    runs-on: ubuntu-22.04
    steps:

    - name: Error if not a tag
      uses: actions/github-script@v7
      if: ${{ ! startsWith(github.ref, 'refs/tags/') }}
      with:
        script: core.setFailed('This action can only be run on tags')

    - name: Check out code into dashboard directory
      uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - name: Extract tag
      id: tag
      run: echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

    - name: Get revision SHA
      id: vars
      run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Set up Docker
      uses: docker/setup-docker-action@v4


    - name: Log in to Quay.io
      uses: docker/login-action@v2
      with:
        registry: quay.io
        username: ${{ secrets.QUAY_USERNAME }}
        password: ${{ secrets.QUAY_ROBOT_TOKEN }}

    - name: Build and push Dashboard image
      uses: docker/build-push-action@v4
      with:
        context: ${{ env.working-directory }}
        file: ${{ env.working-directory }}/Dockerfile
        push: true
        tags: |
          quay.io/kuberay/dashboard:${{ steps.vars.outputs.sha_short }}
          quay.io/kuberay/dashboard:${{ env.tag }}
        labels: |
          GIT_COMMIT=${{ steps.vars.outputs.sha_short }}
        provenance: false
