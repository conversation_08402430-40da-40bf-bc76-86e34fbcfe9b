name: Helm

on:
  push:
    branches:
      - master
      - release-*
  pull_request:
    branches:
      - master
      - release-*

env:
  CHART_DIR: helm-chart

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.actor }}
  cancel-in-progress: true

jobs:
  lint-test:
    name: Lint and <PERSON>

    runs-on: ubuntu-24.04

    strategy:
      matrix:
        chart:
          - kuberay-operator
          - kuberay-apiserver
          - ray-cluster

    steps:
      - name: Determine branch name
        id: get_branch
        run: |
          BRANCH=""
          if [ "${{ github.event_name }}" == "push" ]; then
            BRANCH=${{ github.ref_name }}
          elif [ "${{ github.event_name }}" == "pull_request" ]; then
            BRANCH=${{ github.base_ref }}
          fi
          echo "BRANCH=$BRANCH" >> "$GITHUB_OUTPUT"

      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up He<PERSON>
        uses: azure/setup-helm@v4.3.0
        with:
          version: v3.17.3

      - uses: actions/setup-python@v5.3.0
        with:
          python-version: 3.13

      - name: Install Helm unittest plugin
        run: helm plugin install https://github.com/helm-unittest/helm-unittest.git --version 0.8.1

      - name: Run Helm unittest
        run: helm unittest ${{ env.CHART_DIR }}/${{ matrix.chart }} --file "tests/**/*_test.yaml" --strict --debug

      - name: Set up chart-testing
        uses: helm/chart-testing-action@v2.7.0

      - name: Run chart-testing (list-changed)
        id: list-changed
        env:
          BRANCH: ${{ steps.get_branch.outputs.BRANCH }}
        run: |
          changed=$(ct list-changed --target-branch $BRANCH --chart-dirs ${{ env.CHART_DIR }} | grep ${{ matrix.chart }} || true)
          if [[ -n "$changed" ]]; then
            echo "changed=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Run chart-testing (lint)
        if: steps.list-changed.outputs.changed == 'true'
        env:
          BRANCH: ${{ steps.get_branch.outputs.BRANCH }}
        run: |
          # Run 'helm lint', version checking, YAML schema validation on 'Chart.yaml',
          # YAML linting on 'Chart.yaml' and 'values.yaml', and maintainer.
          # [Doc]: https://github.com/helm/chart-testing/blob/main/doc/ct_lint.md
          ct lint --charts ${{ env.CHART_DIR }}/${{ matrix.chart }} --validate-maintainers=false

      - name: Create kind cluster
        if: steps.list-changed.outputs.changed == 'true'
        uses: helm/kind-action@v1.12.0
        with:
          cluster_name: kind

      - name: Build Docker image (kuberay-operator)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-operator'
        run: |
          cd ray-operator && make docker-image -e IMG=kuberay/operator:local

      - name: Build Docker image (kuberay-apiserver)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-apiserver'
        run: |
          cd apiserver && make docker-image -e IMG=kuberay/apiserver:local

      - name: Build Docker image (security-proxy)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-apiserver'
        run: |
          cd experimental && make docker-image -e IMG=kuberay/security-proxy:local

      - name: Load image to kind cluster (kuberay-operator)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-operator'
        run: |
          kind load docker-image kuberay/operator:local

      - name: Load image to kind cluster (kuberay-apiserver)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-apiserver'
        run: |
          kind load docker-image kuberay/apiserver:local

      - name: Load image to kind cluster (security-proxy)
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'kuberay-apiserver'
        run: |
          kind load docker-image kuberay/security-proxy:local

      - name: Install Custom Resource Definitions to kind cluster
        if: steps.list-changed.outputs.changed == 'true' && matrix.chart == 'ray-cluster'
        working-directory: helm-chart/kuberay-operator
        run: kubectl create -f crds

      - name: Run chart-testing
        if: steps.list-changed.outputs.changed == 'true'
        env:
          BRANCH: ${{ steps.get_branch.outputs.BRANCH }}
        run: |
          # Run 'helm install', 'helm test', and optionally 'helm upgrade' on specified charts.
          # [Doc]: https://github.com/helm/chart-testing/blob/main/doc/ct_install.md
          ct install --charts ${{ env.CHART_DIR}}/${{ matrix.chart }}
