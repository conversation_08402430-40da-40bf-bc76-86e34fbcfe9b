name: Go-build-and-test

on:
  push:
    branches:
    - master
    - release-*
  pull_request:
    branches:
    - master
    - release-*

jobs:
  lint:
    name: <PERSON><PERSON> (pre-commit)
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v3
      - uses: pre-commit/action@v3.0.1

  build_apiserver:
    env:
      working-directory: ./apiserver
    name: Build Apiserver and Docker Images
    runs-on: ubuntu-22.04
    steps:
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: v1.24

      - name: Check out code into the Go module directory
        uses: actions/checkout@v2
        with:
          # When checking out the repository that
          # triggered a workflow, this defaults to the reference or SHA for that event.
          # Default value should work for both pull_request and merge(push) event.
          ref: ${{github.event.pull_request.head.sha}}

      - name: list directories
        working-directory: ${{env.working-directory}}
        run: |
          pwd
          ls -R

      - name: install kubebuilder
        run: |
          wget https://github.com/kubernetes-sigs/kubebuilder/releases/download/v3.0.0/kubebuilder_$(go env GOOS)_$(go env GOARCH)
          sudo mv kubebuilder_$(go env GOOS)_$(go env GOARCH) /usr/local/bin/kubebuilder

      - name: Get revision SHA
        id: vars
        run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

      - name: Get dependencies
        run: go mod download
        working-directory: ${{env.working-directory}}

      - name: Build
        run: go build ./...
        working-directory: ${{env.working-directory}}

      - name: Test
        run: go test ./pkg/... ./cmd/... -race -parallel 4
        working-directory: ${{env.working-directory}}

      - name: Set up Docker
        uses: docker/setup-docker-action@v4

      - name: Build Docker Image - Apiserver
        run: |
          docker build -t kuberay/apiserver:${{ steps.vars.outputs.sha_short }} -f apiserver/Dockerfile .
          docker save -o /tmp/apiserver.tar kuberay/apiserver:${{ steps.vars.outputs.sha_short }}

      - name: Upload Artifact Apiserver
        uses: actions/upload-artifact@v4
        with:
          name: apiserver_img
          path: /tmp/apiserver.tar

      - name: Log in to Quay.io
        uses: docker/login-action@v2
        with:
          registry: quay.io
          username: ${{ secrets.QUAY_USERNAME }}
          password: ${{ secrets.QUAY_ROBOT_TOKEN }}
        if: contains(fromJson('["refs/heads/master"]'), github.ref)

      - name: Push Apiserver to Quay.io
        run: |
          docker image tag kuberay/apiserver:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/apiserver:${{ steps.vars.outputs.sha_short }};
          docker push quay.io/kuberay/apiserver:${{ steps.vars.outputs.sha_short }};
          docker image tag kuberay/apiserver:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/apiserver:nightly;
          docker push quay.io/kuberay/apiserver:nightly
        if: contains(fromJson('["refs/heads/master"]'), github.ref)

  build_apiserversdk:
    env:
      working-directory: ./apiserversdk
    name: Build apiserversdk
    runs-on: ubuntu-22.04
    steps:
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: v1.24

      - name: Check out code into the Go module directory
        uses: actions/checkout@v2
        with:
          ref: ${{github.event.pull_request.head.sha}}

      - name: Lint
        run: make lint
        working-directory: ${{env.working-directory}}

      - name: Get dependencies
        run: go mod download
        working-directory: ${{env.working-directory}}

      - name: Test
        run: make test
        working-directory: ${{env.working-directory}}

  build_security_proxy:
    env:
      working-directory: ./experimental
    name: Build security proxy Binaries and Docker Images
    runs-on: ubuntu-22.04
    steps:

      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: v1.24

      - name: Check out code into the Go module directory
        uses: actions/checkout@v2
        with:
          # When checking out the repository that
          # triggered a workflow, this defaults to the reference or SHA for that event.
          # Default value should work for both pull_request and merge(push) event.
          ref: ${{github.event.pull_request.head.sha}}

      - name: list directories
        working-directory: ${{env.working-directory}}
        run: |
          pwd
          ls -R

      - name: install kubebuilder
        run: |
          wget https://github.com/kubernetes-sigs/kubebuilder/releases/download/v3.0.0/kubebuilder_$(go env GOOS)_$(go env GOARCH)
          sudo mv kubebuilder_$(go env GOOS)_$(go env GOARCH) /usr/local/bin/kubebuilder

      - name: Get revision SHA
        id: vars
        run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

      - name: Get dependencies
        run: go mod download
        working-directory: ${{env.working-directory}}

      - name: Build
        run: make build
        working-directory: ${{env.working-directory}}

      - name: Set up Docker
        uses: docker/setup-docker-action@v4

      - name: Build Docker Image - security proxy
        run: |
          IMG=kuberay/security-proxy:${{ steps.vars.outputs.sha_short }} make docker-image
          docker save -o /tmp/security-proxy.tar kuberay/security-proxy:${{ steps.vars.outputs.sha_short }}
        working-directory: ${{env.working-directory}}

      - name: Upload security proxy artifact
        uses: actions/upload-artifact@v4
        with:
          name: security-proxy_img
          path: /tmp/security-proxy.tar

      - name: Log in to Quay.io
        uses: docker/login-action@v2
        with:
          registry: quay.io
          username: ${{ secrets.QUAY_USERNAME }}
          password: ${{ secrets.QUAY_ROBOT_TOKEN }}
        if: contains(fromJson('["refs/heads/master"]'), github.ref)

      - name: Push security proxy to Quay.io
        run: |
          docker image tag kuberay/security-proxy:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/security-proxy:${{ steps.vars.outputs.sha_short }};
          docker push quay.io/kuberay/security-proxy:${{ steps.vars.outputs.sha_short }};
          docker image tag kuberay/security-proxy:${{ steps.vars.outputs.sha_short }} quay.io/kuberay/security-proxy:nightly;
          docker push quay.io/kuberay/security-proxy:nightly
        if: contains(fromJson('["refs/heads/master"]'), github.ref)

  build_operator:
    env:
      working-directory: ./ray-operator
    name: Build Operator Binaries and Docker Images
    runs-on: ubuntu-22.04
    steps:

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: v1.24

    - name: Check out code into the Go module directory
      uses: actions/checkout@v2
      with:
        # When checking out the repository that
        # triggered a workflow, this defaults to the reference or SHA for that event.
        # Default value should work for both pull_request and merge(push) event.
        ref: ${{github.event.pull_request.head.sha}}

    - name: list directories
      working-directory: ${{env.working-directory}}
      run: |
        pwd
        ls -R

    - name: install kubebuilder
      run: |
        wget https://github.com/kubernetes-sigs/kubebuilder/releases/download/v3.0.0/kubebuilder_$(go env GOOS)_$(go env GOARCH)
        sudo mv kubebuilder_$(go env GOOS)_$(go env GOARCH) /usr/local/bin/kubebuilder

    - name: Get revision SHA
      id: vars
      run: echo "::set-output name=sha_short::$(git rev-parse --short HEAD)"

    - name: Get dependencies
      run: go mod download
      working-directory: ${{env.working-directory}}

    - name: Build
      run: make build
      working-directory: ${{env.working-directory}}

    - name: Test
      run: make test
      working-directory: ${{env.working-directory}}

    - name: Set up Docker
      uses: docker/setup-docker-action@v4

    - name: Build Docker Image - Operator
      run: |
        IMG=kuberay/operator:${{ steps.vars.outputs.sha_short }} make docker-image
        docker save -o /tmp/operator.tar kuberay/operator:${{ steps.vars.outputs.sha_short }}
      working-directory: ${{env.working-directory}}

    - name: Upload Artifact Operator
      uses: actions/upload-artifact@v4
      with:
        name: operator_img
        path: /tmp/operator.tar

    - name: Log in to Quay.io
      uses: docker/login-action@v2
      with:
        registry: quay.io
        username: ${{ secrets.QUAY_USERNAME }}
        password: ${{ secrets.QUAY_ROBOT_TOKEN }}
      if: contains(fromJson('["refs/heads/master"]'), github.ref)

    # Build operators inside the gh runner vm directly and then copy the go binaries to docker images using the Dockerfile.buildx
    - name: Compile linux/amd64 Operator go binary
      env:
        CGO_ENABLED: 1
        GOOS: linux
        GOARCH: amd64
      run: |
        CGO_ENABLED=$CGO_ENABLED GOOS=$GOOS GOARCH=$GOARCH go build -tags strictfipsruntime -a -o manager-$GOARCH main.go
      working-directory: ${{env.working-directory}}
      if: contains(fromJson('["refs/heads/master"]'), github.ref)

    - name: Cross Compile linux/arm64 Operator binary
      env:
        CC: aarch64-linux-gnu-gcc
        CGO_ENABLED: 1
        GOOS: linux
        GOARCH: arm64
      run: |
        sudo apt-get update
        sudo apt-get install -y gcc-aarch64-linux-gnu libc6-dev-arm64-cross
        CC=$CC CGO_ENABLED=$CGO_ENABLED GOOS=$GOOS GOARCH=$GOARCH go build -tags strictfipsruntime -a -o manager-$GOARCH main.go
      working-directory: ${{env.working-directory}}
      if: contains(fromJson('["refs/heads/master"]'), github.ref)

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      if: contains(fromJson('["refs/heads/master"]'), github.ref)

    - name: Build MultiArch Docker Image and Push on Merge
      uses: docker/build-push-action@v5
      env:
        REPO_ORG: kuberay
        REPO_NAME: operator
      with:
        platforms: linux/amd64,linux/arm64
        context: ${{env.working-directory}}
        file: ${{env.working-directory}}/Dockerfile.buildx
        push: true
        provenance: false
        tags: |
          quay.io/${{env.REPO_ORG}}/${{env.REPO_NAME}}:${{ steps.vars.outputs.sha_short }}
          quay.io/${{env.REPO_ORG}}/${{env.REPO_NAME}}:nightly
      if: contains(fromJson('["refs/heads/master"]'), github.ref)

  build_kubectl-plugin:
    env:
      working-directory: ./kubectl-plugin
    name: Build Ray Kubectl plugin
    runs-on: ubuntu-22.04
    steps:
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: v1.24

      - name: Check out code into the Go module directory
        uses: actions/checkout@v2
        with:
          # When checking out the repository that
          # triggered a workflow, this defaults to the reference or SHA for that event.
          # Default value should work for both pull_request and merge(push) event.
          ref: ${{github.event.pull_request.head.sha}}

      - name: Get dependencies
        run: go mod download
        working-directory: ${{env.working-directory}}

      - name: Build CLI
        run: go build -o kubectl-ray -a ./cmd/kubectl-ray.go
        working-directory: ${{env.working-directory}}

      - name: Test
        run: go test ./pkg/... -race -parallel 4
        working-directory: ${{env.working-directory}}

  python-client-test:
    runs-on: ubuntu-22.04
    name: Python Client Test
    steps:
      - name: Set up Docker
        uses: docker/setup-docker-action@v4

      - name: Install Kind
        run: |
          curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.22.0/kind-linux-amd64
          chmod +x ./kind
          sudo mv ./kind /usr/local/bin/kind
          kind create cluster
        shell: bash

      - name: Checkout Python
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.x'

      - name: Install package and run unittest for Python client
        working-directory: ./clients/python-client
        run: |
          pip install -e .
          python3 -m unittest discover 'python_client_test/'
