name: KubeRay feature request
description: Suggest an idea for KubeRay project
title: "[Feature] "
labels: enhancement, triage
body:
  - type: markdown
    attributes:
      value: |
        Thank you for finding the time to propose a new feature!
        We really appreciate the community efforts to improve KubeRay.
  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please make sure to search in the [issues](https://github.com/ray-project/kuberay/issues) first to see
        whether the same feature was requested already.
      options:
        - label: >
            I had searched in the [issues](https://github.com/ray-project/kuberay/issues) and found no similar
            feature requirement.
          required: true
  - type: textarea
    attributes:
      label: Description
      description: A short description of your feature

  - type: textarea
    attributes:
      label: Use case
      description: >
        Describe the use case of your feature request. It will help us understand and
        prioritize the feature request.
      placeholder: >
        Rather than telling us how you might implement this feature, try to take a
        step back and describe what you are trying to achieve.
  - type: textarea
    attributes:
      label: Related issues
      description: Is there currently another issue associated with this?

  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        This is absolutely not required, but we are happy to guide you in the contribution process
        especially if you already have a good understanding of how to implement the feature.
      options:
        - label: Yes I am willing to submit a PR!

  - type: markdown
    attributes:
      value: "Thanks for completing our form!"
