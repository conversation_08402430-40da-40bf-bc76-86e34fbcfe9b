version: 2
updates:

  # Maintain go dependencies for sub-projects
  - package-ecosystem: "gomod"
    directories:
      - "/experimental"
      - "/ray-operator"
      - "/apiserver"
      - "/kubectl-plugin"
      - "/proto"
    schedule:
      interval: "weekly"
    groups:
      kubernetes:
        patterns:
          - "k8s.io/*"
          - "sigs.k8s.io/*"
      google-golang:
        patterns:
          - "google.golang.org/*"
      github-dependencies:
        patterns:
          - "github.com/*"
      all-dependencies: # for all other dependencies not listed above
        patterns:
          - "*"
