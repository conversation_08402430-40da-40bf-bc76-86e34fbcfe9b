# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: io
layout:
- go.kubebuilder.io/v3
multigroup: true
projectName: ray-operator
repo: github.com/ray-project/kuberay/ray-operator
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: io
  group: ray
  kind: RayCluster
  path: github.com/ray-project/kuberay/ray-operator/apis/ray/v1
  version: v1
  webhooks:
    defaulting: true
    validation: true
    webhookVersion: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: io
  group: ray
  kind: RayService
  path: github.com/ray-project/kuberay/ray-operator/apis/ray/v1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: io
  group: ray
  kind: RayJob
  path: github.com/ray-project/kuberay/ray-operator/apis/ray/v1
  version: v1
version: "3"
