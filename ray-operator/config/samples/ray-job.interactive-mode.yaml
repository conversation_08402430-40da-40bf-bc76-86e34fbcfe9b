apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: rayjob-interactive-mode
spec:
  # InteractiveMode means <PERSON><PERSON><PERSON><PERSON> doesn't submit the job for you.
  # <PERSON><PERSON>R<PERSON> will create the RayJob and transition it to the Waiting state.
  # The user needs to submit the job manually via the `ray job submit` command
  # and then update the `spec.jobId` field with the job ID.
  # After that, <PERSON><PERSON><PERSON><PERSON> will handle the rest of the lifecycle for the RayJob.
  submissionMode: InteractiveMode
  # User needs to update this field with the job ID after submitting the job
  jobId: ""
  rayClusterSpec:
    headGroupSpec:
      rayStartParams: {}
      template:
        spec:
          containers:
          - image: rayproject/ray:2.46.0
            name: ray-head
            ports:
            - containerPort: 6379
              name: gcs-server
            - containerPort: 8265
              name: dashboard
            - containerPort: 10001
              name: client
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
              requests:
                cpu: "2"
                memory: 4Gi
    rayVersion: 2.46.0
    workerGroupSpecs:
    - groupName: default-group
      replicas: 1
      rayStartParams: {}
      template:
        spec:
          containers:
          - image: rayproject/ray:2.46.0
            name: ray-worker
            resources:
              limits:
                cpu: "2"
                memory: 4Gi
              requests:
                cpu: "2"
                memory: 4Gi
