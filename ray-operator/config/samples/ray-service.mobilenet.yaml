apiVersion: ray.io/v1
kind: RayService
metadata:
  name: rayservice-mobilenet
spec:
  serveConfigV2: |
    applications:
      - name: mobilenet
        import_path: mobilenet.mobilenet:app
        runtime_env:
          working_dir: "https://github.com/ray-project/serve_config_examples/archive/b393e77bbd6aba0881e3d94c05f968f05a387b96.zip"
          pip: ["python-multipart==0.0.6"]
        deployments:
          - name: ImageClassifier
            num_replicas: 2
            ray_actor_options:
              num_cpus: 1
  rayClusterConfig:
    rayVersion: '2.46.0' # should match the Ray version in the image of the containers
    ######################headGroupSpecs#################################
    # Ray head pod template.
    headGroupSpec:
      rayStartParams: {}
      #pod template
      template:
        spec:
          containers:
          - name: ray-head
            image: rayproject/ray-ml:2.46.0.0e19ea-py39-cpu
            resources:
              limits:
                cpu: 1
                memory: 4Gi
              requests:
                cpu: 1
                memory: 4Gi
    workerGroupSpecs:
    # the pod replicas in this group typed worker
    - replicas: 1
      minReplicas: 1
      maxReplicas: 5
      # logical group name, for this called small-group, also can be functional
      groupName: worker
      rayStartParams: {}
      #pod template
      template:
        spec:
          containers:
          - name: ray-worker # must consist of lower case alphanumeric characters or '-', and must start and end with an alphanumeric character (e.g. 'my-name',  or '123-abc'
            image: rayproject/ray-ml:2.46.0.0e19ea-py39-cpu
            resources:
              limits:
                cpu: 1
                memory: 4Gi
              requests:
                cpu: 1
                memory: 4Gi
