apiVersion: ray.io/v1
kind: RayService
metadata:
  name: text-summarizer
spec:
  serveConfigV2: |
    applications:
      - name: text_summarizer
        import_path: text_summarizer.text_summarizer:deployment
        runtime_env:
          working_dir: "https://github.com/ray-project/serve_config_examples/archive/607d1264c2c998e4a85eaf5403efcd3bc9ed3039.zip"
  rayClusterConfig:
    rayVersion: '2.46.0' # Should match the Ray version in the image of the containers
    ######################headGroupSpecs#################################
    # Ray head pod template.
    headGroupSpec:
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-head
            image: rayproject/ray-ml:2.46.0.0e19ea-py39-gpu
            volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
            resources:
              limits:
                cpu: "2"
                memory: "8G"
              requests:
                cpu: "2"
                memory: "8G"
          volumes:
          - name: ray-logs
            emptyDir: {}
    workerGroupSpecs:
    # The pod replicas in this group typed worker
    - replicas: 1
      minReplicas: 1
      maxReplicas: 10
      groupName: gpu-group
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-worker
            image: rayproject/ray-ml:2.46.0.0e19ea-py39-gpu
            resources:
              limits:
                cpu: 4
                memory: "16G"
                nvidia.com/gpu: 1
              requests:
                cpu: 3
                memory: "12G"
                nvidia.com/gpu: 1
          # Please add the following taints to the GPU node.
          tolerations:
          - key: "ray.io/node-type"
            operator: "Equal"
            value: "worker"
            effect: "NoSchedule"
