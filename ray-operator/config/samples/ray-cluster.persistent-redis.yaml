apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: raycluster-external-redis
spec:
  rayVersion: '2.46.0'
  gcsFaultToleranceOptions:
    # In most cases, you don't need to set `externalStorageNamespace` because KubeRay will
    # automatically set it to the UID of RayCluster. Only modify this annotation if you fully understand
    # the behaviors of the Ray GCS FT and RayService to avoid misconfiguration.
    # [Example]:
    # externalStorageNamespace: "my-raycluster-storage"
    redisAddress: "redis:6379"
    redisPassword:
      valueFrom:
        secretKeyRef:
          name: redis-password-secret
          key: password
  headGroupSpec:
    # The `rayStartParams` are used to configure the `ray start` command.
    # See https://github.com/ray-project/kuberay/blob/master/docs/guidance/rayStartParams.md for the default settings of `rayStartParams` in KubeRay.
    # See https://docs.ray.io/en/latest/cluster/cli.html#ray-start for all available options in `rayStartParams`.
    rayStartParams:
      # Setting "num-cpus: 0" to avoid any Ray actors or tasks being scheduled on the Ray head Pod.
      num-cpus: "0"
    # Pod template
    template:
      spec:
        containers:
        - name: ray-head
          image: rayproject/ray:2.46.0
          resources:
            limits:
              cpu: "1"
            requests:
              cpu: "1"
          ports:
          - containerPort: 6379
            name: redis
          - containerPort: 8265
            name: dashboard
          - containerPort: 10001
            name: client
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
        volumes:
        - name: ray-logs
          emptyDir: {}
  workerGroupSpecs:
  # the pod replicas in this group typed worker
  - replicas: 1
    minReplicas: 1
    maxReplicas: 10
    groupName: small-group
    # The `rayStartParams` are used to configure the `ray start` command.
    # See https://github.com/ray-project/kuberay/blob/master/docs/guidance/rayStartParams.md for the default settings of `rayStartParams` in KubeRay.
    # See https://docs.ray.io/en/latest/cluster/cli.html#ray-start for all available options in `rayStartParams`.
    rayStartParams: {}
    # Pod template
    template:
      spec:
        containers:
        - name: ray-worker
          image: rayproject/ray:2.46.0
          volumeMounts:
          - mountPath: /tmp/ray
            name: ray-logs
          resources:
            limits:
              cpu: "1"
            requests:
              cpu: "1"
        volumes:
        - name: ray-logs
          emptyDir: {}
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: redis-config
  labels:
    app: redis
data:
  redis.conf: |-
    dir /data
    port 6379
    bind 0.0.0.0
    protected-mode no
    requirepass 5241590000000000
    pidfile /data/redis-6379.pid
    # Dump a backup every 60s, if there are 1000 writes since the prev. backup.
    save 60 1000
    dbfilename dump.rdb
    # Enable the append-only log file.
    appendonly yes
    # Sync the log to disk every second.
    # Alternatives are "no" and "always" (every write).
    appendfsync everysec
    # These are the default values, change if desired.
    appendfilename "appendonly.aof"
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - name: redis
    port: 6379
  selector:
    app: redis
---
# This volume claim will use the default storage class for your cluster/provider.
# On GCP, this is a persistent disk, which is more performant than GCS FUSE
# (which does not support append operations).
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data
spec:
  # choose a storageClassName provided by your Kubernetes:
  # storageClassName: standard-rwo
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 8Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7.4.0
        command:
        - "sh"
        - "-c"
        - "redis-server /usr/local/etc/redis/redis.conf"
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: config
          mountPath: /usr/local/etc/redis/redis.conf
          subPath: redis.conf
        - name: redis-data
          mountPath: /data
      volumes:
      - name: config
        configMap:
          name: redis-config
      - name: redis-data
        persistentVolumeClaim:
          claimName: redis-data
---
# Redis password
apiVersion: v1
kind: Secret
metadata:
  name: redis-password-secret
type: Opaque
data:
  # echo -n "5241590000000000" | base64
  password: NTI0MTU5MDAwMDAwMDAwMA==
