apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rayclient-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
spec:
  rules:
  - host: localhost
    http:
      paths:
      - backend:
          service:
            name: raycluster-tls-head-svc
            port:
              number: 10001
        path: /
        pathType: Prefix
