apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: raycluster-uv
spec:
  rayVersion: '2.46.0' # should match the Ray version in the image of the containers
  headGroupSpec:
    rayStartParams: {}
    template:
      spec:
        containers:
        - name: ray-head
          image: rayproject/ray:2.46.0
          env:
          - name: RAY_RUNTIME_ENV_HOOK
            value: ray._private.runtime_env.uv_runtime_env_hook.hook
          resources:
            limits:
              cpu: 1
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 2Gi
          ports:
          - containerPort: 6379
            name: gcs-server
          - containerPort: 8265 # Ray dashboard
            name: dashboard
          - containerPort: 10001
            name: client
          volumeMounts:
          - mountPath: /home/<USER>/samples
            name: code-sample
        volumes:
        - name: code-sample
          configMap:
            name: ray-uv-code-sample
            items:
            - key: sample_code.py
              path: sample_code.py

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ray-uv-code-sample
data:
  sample_code.py: |
    import emoji
    import ray

    @ray.remote
    def f():
        return emoji.emojize('Python is :thumbs_up:')

    # Execute 10 copies of f across a cluster.
    print(ray.get([f.remote() for _ in range(10)]))
