# This RayJob is based on the "Finetuning a Pytorch Image Classifier with Ray Train" example in the Ray documentation.
# See https://docs.ray.io/en/latest/train/examples/pytorch/pytorch_resnet_finetune.html for more details.
apiVersion: ray.io/v1
kind: RayJob
metadata:
  generateName: pytorch-image-classifier-
spec:
  shutdownAfterJobFinishes: true
  entrypoint: python ray-operator/config/samples/pytorch-resnet-image-classifier/fine-tune-pytorch-resnet-image-classifier.py
  runtimeEnvYAML: |
    pip:
      - numpy
      - datasets
      - torch
      - torchvision
      - transformers>=4.19.1
    working_dir: "https://github.com/ray-project/kuberay/archive/master.zip"
  rayClusterSpec:
    rayVersion: '2.46.0'
    headGroupSpec:
      rayStartParams:
        dashboard-host: '0.0.0.0'
      template:
        metadata:
          annotations:
            gke-gcsfuse/volumes: "true"
            gke-gcsfuse/cpu-limit: "0"
            gke-gcsfuse/memory-limit: 5Gi
            gke-gcsfuse/ephemeral-storage-limit: 10Gi
        spec:
          serviceAccountName: pytorch-distributed-training
          containers:
          - name: ray-head
            image: rayproject/ray:2.46.0
            env:
            - name: NUM_WORKERS
              value: "4"
            ports:
            - containerPort: 6379
              name: gcs-server
            - containerPort: 8265
              name: dashboard
            - containerPort: 10001
              name: client
            resources:
              limits:
                cpu: "1"
                memory: "8G"
              requests:
                cpu: "1"
                memory: "8G"
            volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
            - mountPath: /mnt/cluster_storage
              name: cluster-storage
          volumes:
          - name: ray-logs
            emptyDir: {}
          - name: cluster-storage
            csi:
              driver: gcsfuse.csi.storage.gke.io
              volumeAttributes:
                bucketName: GCS_BUCKET
                mountOptions: "implicit-dirs,uid=1000,gid=100"
    workerGroupSpecs:
    - replicas: 4
      minReplicas: 4
      maxReplicas: 4
      groupName: gpu-group
      rayStartParams:
        dashboard-host: '0.0.0.0'
      template:
        metadata:
          annotations:
            gke-gcsfuse/volumes: "true"
            gke-gcsfuse/cpu-limit: "0"
            gke-gcsfuse/memory-limit: 5Gi
            gke-gcsfuse/ephemeral-storage-limit: 10Gi
        spec:
          serviceAccountName: pytorch-distributed-training
          tolerations:
          - key: "nvidia.com/gpu"
            operator: "Exists"
            effect: "NoSchedule"
          containers:
          - name: ray-worker
            image: rayproject/ray-ml:2.46.0.0e19ea-py39
            resources:
              limits:
                cpu: "1"
                memory: "8G"
                nvidia.com/gpu: "1"
              requests:
                cpu: "1"
                memory: "8G"
                nvidia.com/gpu: "1"
            volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
            - mountPath: /mnt/cluster_storage
              name: cluster-storage
          volumes:
          - name: ray-logs
            emptyDir: {}
          - name: cluster-storage
            csi:
              driver: gcsfuse.csi.storage.gke.io
              volumeAttributes:
                bucketName: GCS_BUCKET
                mountOptions: "implicit-dirs,uid=1000,gid=100"
