apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: rayjob-pytorch-mnist
spec:
  shutdownAfterJobFinishes: false
  entrypoint: python ray-operator/config/samples/pytorch-mnist/ray_train_pytorch_mnist.py
  runtimeEnvYAML: |
    pip:
      - torch
      - torchvision
    working_dir: "https://github.com/ray-project/kuberay/archive/master.zip"
    env_vars:
      NUM_WORKERS: "2"
      CPUS_PER_WORKER: "2"

  # rayClusterSpec specifies the RayCluster instance to be created by the RayJob controller.
  rayClusterSpec:
    rayVersion: '2.46.0'
    headGroupSpec:
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-head
            image: rayproject/ray:2.46.0
            ports:
            - containerPort: 6379
              name: gcs-server
            - containerPort: 8265 # Ray dashboard
              name: dashboard
            - containerPort: 10001
              name: client
            resources:
              limits:
                cpu: "1"
                memory: "4Gi"
              requests:
                cpu: "1"
                memory: "4Gi"
    workerGroupSpecs:
    - replicas: 2
      minReplicas: 1
      maxReplicas: 5
      groupName: small-group
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-worker
            image: rayproject/ray:2.46.0
            resources:
              limits:
                cpu: "3"
                memory: "4Gi"
              requests:
                cpu: "3"
                memory: "4Gi"
