apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: image-resize
spec:
  shutdownAfterJobFinishes: false
  entrypoint: python ray-operator/config/samples/ray-data-image-resize/ray_data_image_resize.py
  runtimeEnvYAML: |
    pip:
      - torch
      - torchvision
      - numpy
      - google-cloud-storage
    working_dir: "https://github.com/ray-project/kuberay/archive/master.zip"
    env_vars:
      BUCKET_NAME: ray-images
      BUCKET_PREFIX: images
  # rayClusterSpec specifies the RayCluster instance to be created by the RayJob controller.
  rayClusterSpec:
    rayVersion: '2.46.0'
    headGroupSpec:
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-head
            image: rayproject/ray:2.46.0
            ports:
            - containerPort: 6379
              name: gcs-server
            - containerPort: 8265 # Ray dashboard
              name: dashboard
            - containerPort: 10001
              name: client
            resources:
              limits:
                cpu: "2"
                memory: "4Gi"
              requests:
                cpu: "2"
                memory: "4Gi"
    workerGroupSpecs:
    - replicas: 4
      minReplicas: 1
      maxReplicas: 5
      groupName: small-group
      rayStartParams: {}
      # Pod template
      template:
        spec:
          containers:
          - name: ray-worker
            image: rayproject/ray:2.46.0
            resources:
              limits:
                cpu: "2"
                memory: "4Gi"
              requests:
                cpu: "2"
                memory: "4Gi"
