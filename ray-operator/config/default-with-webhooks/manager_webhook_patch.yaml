apiVersion: apps/v1
kind: Deployment
metadata:
  name: kuberay-operator
  namespace: system
spec:
  template:
    spec:
      containers:
      - name: kuberay-operator
        env:
        - name: ENABLE_WEBHOOKS
          value: "true"
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: webhook-server-cert
