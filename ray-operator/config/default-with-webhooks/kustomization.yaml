# Adds namespace to all resources.
namespace: ray-system

# Value of this field is prepended to the
# names of all resources, e.g. a deployment named
# "wordpress" becomes "alices-wordpress".
# Note that it should also match with the prefix (text before '-') of the namespace
# field above.
#namePrefix: ray-operator-

# Labels to add to all resources and selectors.
#commonLabels:
#  someName: someValue

#- ../prometheus

images:
- name: kuberay/operator
  newName: quay.io/kuberay/operator
  newTag: nightly


replacements:
- source:
    fieldPath: .metadata.namespace
    group: cert-manager.io
    kind: Certificate
    name: serving-cert
    version: v1
  targets:
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
    select:
      kind: ValidatingWebhookConfiguration
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
    select:
      kind: MutatingWebhookConfiguration
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
    select:
      kind: CustomResourceDefinition
- source:
    fieldPath: .metadata.name
    group: cert-manager.io
    kind: Certificate
    name: serving-cert
    version: v1
  targets:
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
      index: 1
    select:
      kind: ValidatingWebhookConfiguration
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
      index: 1
    select:
      kind: MutatingWebhookConfiguration
  - fieldPaths:
    - .metadata.annotations.[cert-manager.io/inject-ca-from]
    options:
      create: true
      delimiter: /
      index: 1
    select:
      kind: CustomResourceDefinition
- source:
    fieldPath: .metadata.name
    kind: Service
    name: webhook-service
    version: v1
  targets:
  - fieldPaths:
    - .spec.dnsNames.0
    - .spec.dnsNames.1
    options:
      create: true
      delimiter: .
    select:
      group: cert-manager.io
      kind: Certificate
      version: v1
- source:
    fieldPath: .metadata.namespace
    kind: Service
    name: webhook-service
    version: v1
  targets:
  - fieldPaths:
    - .spec.dnsNames.0
    - .spec.dnsNames.1
    options:
      create: true
      delimiter: .
      index: 1
    select:
      group: cert-manager.io
      kind: Certificate
      version: v1
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../crd
- ../rbac
- ../manager
- ../webhook
- ../certmanager
- namespace.yaml
patches:
- path: manager_webhook_patch.yaml
- path: webhookcainjection_patch.yaml
