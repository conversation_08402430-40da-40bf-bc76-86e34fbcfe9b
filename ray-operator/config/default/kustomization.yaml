# Adds namespace to all resources.
namespace: default

# Value of this field is prepended to the
# names of all resources, e.g. a deployment named
# "wordpress" becomes "alices-wordpress".
# Note that it should also match with the prefix (text before '-') of the namespace
# field above.
#namePrefix: ray-operator-

# Labels to add to all resources and selectors.
#commonLabels:
#  someName: someValue

# [PROMETHEUS] To enable prometheus monitor, uncomment all sections with 'PROMETHEUS'.
#- ../prometheus

images:
- name: kuberay/operator
  newName: quay.io/kuberay/operator
  newTag: nightly
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../crd
- ../rbac
- ../manager
