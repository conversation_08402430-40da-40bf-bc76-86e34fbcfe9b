apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/path: /metrics
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
  labels:
    app.kubernetes.io/name: kuberay
    app.kubernetes.io/component: kuberay-operator
  name: kuberay-operator
spec:
  ports:
    - name: monitoring-port
      port: 8080
      targetPort: 8080
  selector:
    app.kubernetes.io/name: kuberay
    app.kubernetes.io/component: kuberay-operator
  type: ClusterIP
