---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-ray-io-v1-raycluster
  failurePolicy: Fail
  name: vraycluster.kb.io
  rules:
  - apiGroups:
    - ray.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - rayclusters
  sideEffects: None
