// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	rayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	typedrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/typed/ray/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeRayJobs implements RayJobInterface
type fakeRayJobs struct {
	*gentype.FakeClientWithListAndApply[*v1.<PERSON><PERSON>ob, *v1.RayJobList, *rayv1.RayJobApplyConfiguration]
	Fake *FakeRayV1
}

func newFakeRayJobs(fake *FakeRayV1, namespace string) typedrayv1.RayJobInterface {
	return &fakeRayJobs{
		gentype.NewFakeClientWithListAndApply[*v1.<PERSON><PERSON><PERSON>, *v1.<PERSON><PERSON><PERSON><PERSON><PERSON>, *rayv1.RayJobApplyConfiguration](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("rayjobs"),
			v1.SchemeGroupVersion.WithKind("RayJob"),
			func() *v1.RayJob { return &v1.RayJob{} },
			func() *v1.RayJobList { return &v1.RayJobList{} },
			func(dst, src *v1.RayJobList) { dst.ListMeta = src.ListMeta },
			func(list *v1.RayJobList) []*v1.RayJob { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.RayJobList, items []*v1.RayJob) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}
