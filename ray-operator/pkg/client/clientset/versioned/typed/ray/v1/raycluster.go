// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	applyconfigurationrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	scheme "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// RayClustersGetter has a method to return a RayClusterInterface.
// A group's client should implement this interface.
type RayClustersGetter interface {
	RayClusters(namespace string) RayClusterInterface
}

// RayClusterInterface has methods to work with RayCluster resources.
type RayClusterInterface interface {
	Create(ctx context.Context, rayCluster *rayv1.RayCluster, opts metav1.CreateOptions) (*rayv1.RayCluster, error)
	Update(ctx context.Context, rayCluster *rayv1.RayCluster, opts metav1.UpdateOptions) (*rayv1.RayCluster, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, rayCluster *rayv1.RayCluster, opts metav1.UpdateOptions) (*rayv1.RayCluster, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*rayv1.RayCluster, error)
	List(ctx context.Context, opts metav1.ListOptions) (*rayv1.RayClusterList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *rayv1.RayCluster, err error)
	Apply(ctx context.Context, rayCluster *applyconfigurationrayv1.RayClusterApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayCluster, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, rayCluster *applyconfigurationrayv1.RayClusterApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayCluster, err error)
	RayClusterExpansion
}

// rayClusters implements RayClusterInterface
type rayClusters struct {
	*gentype.ClientWithListAndApply[*rayv1.RayCluster, *rayv1.RayClusterList, *applyconfigurationrayv1.RayClusterApplyConfiguration]
}

// newRayClusters returns a RayClusters
func newRayClusters(c *RayV1Client, namespace string) *rayClusters {
	return &rayClusters{
		gentype.NewClientWithListAndApply[*rayv1.RayCluster, *rayv1.RayClusterList, *applyconfigurationrayv1.RayClusterApplyConfiguration](
			"rayclusters",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *rayv1.RayCluster { return &rayv1.RayCluster{} },
			func() *rayv1.RayClusterList { return &rayv1.RayClusterList{} },
		),
	}
}
