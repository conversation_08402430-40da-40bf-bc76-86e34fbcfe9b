// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	rayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	typedrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/typed/ray/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeRayServices implements RayServiceInterface
type fakeRayServices struct {
	*gentype.FakeClientWithListAndApply[*v1.RayService, *v1.RayServiceList, *rayv1.RayServiceApplyConfiguration]
	Fake *FakeRayV1
}

func newFakeRayServices(fake *FakeRayV1, namespace string) typedrayv1.RayServiceInterface {
	return &fakeRayServices{
		gentype.NewFakeClientWithListAndApply[*v1.RayService, *v1.RayServiceList, *rayv1.RayServiceApplyConfiguration](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("rayservices"),
			v1.SchemeGroupVersion.WithKind("RayService"),
			func() *v1.RayService { return &v1.RayService{} },
			func() *v1.RayServiceList { return &v1.RayServiceList{} },
			func(dst, src *v1.RayServiceList) { dst.ListMeta = src.ListMeta },
			func(list *v1.RayServiceList) []*v1.RayService { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.RayServiceList, items []*v1.RayService) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}
