// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	http "net/http"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	scheme "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/scheme"
	rest "k8s.io/client-go/rest"
)

type RayV1Interface interface {
	RESTClient() rest.Interface
	RayClustersGetter
	RayJobsGetter
	RayServicesGetter
}

// RayV1Client is used to interact with features provided by the ray.io group.
type RayV1Client struct {
	restClient rest.Interface
}

func (c *RayV1Client) RayClusters(namespace string) RayClusterInterface {
	return newRayClusters(c, namespace)
}

func (c *RayV1Client) RayJobs(namespace string) RayJobInterface {
	return newRayJobs(c, namespace)
}

func (c *RayV1Client) RayServices(namespace string) RayServiceInterface {
	return newRayServices(c, namespace)
}

// NewForConfig creates a new RayV1Client for the given config.
// NewForConfig is equivalent to NewForConfigAndClient(c, httpClient),
// where httpClient was generated with rest.HTTPClientFor(c).
func NewForConfig(c *rest.Config) (*RayV1Client, error) {
	config := *c
	setConfigDefaults(&config)
	httpClient, err := rest.HTTPClientFor(&config)
	if err != nil {
		return nil, err
	}
	return NewForConfigAndClient(&config, httpClient)
}

// NewForConfigAndClient creates a new RayV1Client for the given config and http client.
// Note the http client provided takes precedence over the configured transport values.
func NewForConfigAndClient(c *rest.Config, h *http.Client) (*RayV1Client, error) {
	config := *c
	setConfigDefaults(&config)
	client, err := rest.RESTClientForConfigAndClient(&config, h)
	if err != nil {
		return nil, err
	}
	return &RayV1Client{client}, nil
}

// NewForConfigOrDie creates a new RayV1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *RayV1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new RayV1Client for the given RESTClient.
func New(c rest.Interface) *RayV1Client {
	return &RayV1Client{c}
}

func setConfigDefaults(config *rest.Config) {
	gv := rayv1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = rest.CodecFactoryForGeneratedClient(scheme.Scheme, scheme.Codecs).WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *RayV1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
