// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/typed/ray/v1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeRayV1 struct {
	*testing.Fake
}

func (c *FakeRayV1) RayClusters(namespace string) v1.RayClusterInterface {
	return newFakeRayClusters(c, namespace)
}

func (c *FakeRayV1) RayJobs(namespace string) v1.RayJobInterface {
	return newFakeRayJobs(c, namespace)
}

func (c *FakeRayV1) RayServices(namespace string) v1.RayServiceInterface {
	return newFakeRayServices(c, namespace)
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeRayV1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
