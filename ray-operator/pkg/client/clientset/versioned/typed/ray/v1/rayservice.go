// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	applyconfigurationrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	scheme "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// RayServicesGetter has a method to return a RayServiceInterface.
// A group's client should implement this interface.
type RayServicesGetter interface {
	RayServices(namespace string) RayServiceInterface
}

// RayServiceInterface has methods to work with RayService resources.
type RayServiceInterface interface {
	Create(ctx context.Context, rayService *rayv1.RayService, opts metav1.CreateOptions) (*rayv1.RayService, error)
	Update(ctx context.Context, rayService *rayv1.RayService, opts metav1.UpdateOptions) (*rayv1.RayService, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, rayService *rayv1.RayService, opts metav1.UpdateOptions) (*rayv1.RayService, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*rayv1.RayService, error)
	List(ctx context.Context, opts metav1.ListOptions) (*rayv1.RayServiceList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *rayv1.RayService, err error)
	Apply(ctx context.Context, rayService *applyconfigurationrayv1.RayServiceApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayService, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, rayService *applyconfigurationrayv1.RayServiceApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayService, err error)
	RayServiceExpansion
}

// rayServices implements RayServiceInterface
type rayServices struct {
	*gentype.ClientWithListAndApply[*rayv1.RayService, *rayv1.RayServiceList, *applyconfigurationrayv1.RayServiceApplyConfiguration]
}

// newRayServices returns a RayServices
func newRayServices(c *RayV1Client, namespace string) *rayServices {
	return &rayServices{
		gentype.NewClientWithListAndApply[*rayv1.RayService, *rayv1.RayServiceList, *applyconfigurationrayv1.RayServiceApplyConfiguration](
			"rayservices",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *rayv1.RayService { return &rayv1.RayService{} },
			func() *rayv1.RayServiceList { return &rayv1.RayServiceList{} },
		),
	}
}
