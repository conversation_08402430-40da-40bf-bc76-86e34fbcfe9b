// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	rayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	typedrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/typed/ray/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeRayClusters implements RayClusterInterface
type fakeRayClusters struct {
	*gentype.FakeClientWithListAndApply[*v1.RayCluster, *v1.RayClusterList, *rayv1.RayClusterApplyConfiguration]
	Fake *FakeRayV1
}

func newFakeRayClusters(fake *FakeRayV1, namespace string) typedrayv1.RayClusterInterface {
	return &fakeRayClusters{
		gentype.NewFakeClientWithListAndApply[*v1.RayCluster, *v1.RayClusterList, *rayv1.RayClusterApplyConfiguration](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("rayclusters"),
			v1.SchemeGroupVersion.WithKind("RayCluster"),
			func() *v1.RayCluster { return &v1.RayCluster{} },
			func() *v1.RayClusterList { return &v1.RayClusterList{} },
			func(dst, src *v1.RayClusterList) { dst.ListMeta = src.ListMeta },
			func(list *v1.RayClusterList) []*v1.RayCluster { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.RayClusterList, items []*v1.RayCluster) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}
