// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	applyconfigurationrayv1 "github.com/ray-project/kuberay/ray-operator/pkg/client/applyconfiguration/ray/v1"
	scheme "github.com/ray-project/kuberay/ray-operator/pkg/client/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// RayJobsGetter has a method to return a RayJobInterface.
// A group's client should implement this interface.
type RayJobsGetter interface {
	RayJobs(namespace string) RayJobInterface
}

// RayJobInterface has methods to work with RayJob resources.
type RayJobInterface interface {
	Create(ctx context.Context, rayJob *rayv1.RayJ<PERSON>, opts metav1.CreateOptions) (*rayv1.RayJob, error)
	Update(ctx context.Context, rayJob *rayv1.<PERSON><PERSON><PERSON>, opts metav1.UpdateOptions) (*rayv1.RayJob, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, rayJob *rayv1.RayJob, opts metav1.UpdateOptions) (*rayv1.RayJob, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*rayv1.RayJob, error)
	List(ctx context.Context, opts metav1.ListOptions) (*rayv1.RayJobList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *rayv1.RayJob, err error)
	Apply(ctx context.Context, rayJob *applyconfigurationrayv1.RayJobApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayJob, err error)
	// Add a +genclient:noStatus comment above the type to avoid generating ApplyStatus().
	ApplyStatus(ctx context.Context, rayJob *applyconfigurationrayv1.RayJobApplyConfiguration, opts metav1.ApplyOptions) (result *rayv1.RayJob, err error)
	RayJobExpansion
}

// rayJobs implements RayJobInterface
type rayJobs struct {
	*gentype.ClientWithListAndApply[*rayv1.RayJob, *rayv1.RayJobList, *applyconfigurationrayv1.RayJobApplyConfiguration]
}

// newRayJobs returns a RayJobs
func newRayJobs(c *RayV1Client, namespace string) *rayJobs {
	return &rayJobs{
		gentype.NewClientWithListAndApply[*rayv1.RayJob, *rayv1.RayJobList, *applyconfigurationrayv1.RayJobApplyConfiguration](
			"rayjobs",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *rayv1.RayJob { return &rayv1.RayJob{} },
			func() *rayv1.RayJobList { return &rayv1.RayJobList{} },
		),
	}
}
