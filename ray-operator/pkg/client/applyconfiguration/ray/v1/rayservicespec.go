// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	corev1 "k8s.io/api/core/v1"
)

// RayServiceSpecApplyConfiguration represents a declarative configuration of the RayServiceSpec type for use
// with apply.
type RayServiceSpecApplyConfiguration struct {
	ServiceUnhealthySecondThreshold    *int32                                       `json:"serviceUnhealthySecondThreshold,omitempty"`
	DeploymentUnhealthySecondThreshold *int32                                       `json:"deploymentUnhealthySecondThreshold,omitempty"`
	ServeService                       *corev1.Service                              `json:"serveService,omitempty"`
	UpgradeStrategy                    *RayServiceUpgradeStrategyApplyConfiguration `json:"upgradeStrategy,omitempty"`
	ServeConfigV2                      *string                                      `json:"serveConfigV2,omitempty"`
	RayClusterSpec                     *RayClusterSpecApplyConfiguration            `json:"rayClusterConfig,omitempty"`
	ExcludeHeadPodFromServeSvc         *bool                                        `json:"excludeHeadPodFromServeSvc,omitempty"`
}

// RayServiceSpecApplyConfiguration constructs a declarative configuration of the RayServiceSpec type for use with
// apply.
func RayServiceSpec() *RayServiceSpecApplyConfiguration {
	return &RayServiceSpecApplyConfiguration{}
}

// WithServiceUnhealthySecondThreshold sets the ServiceUnhealthySecondThreshold field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ServiceUnhealthySecondThreshold field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithServiceUnhealthySecondThreshold(value int32) *RayServiceSpecApplyConfiguration {
	b.ServiceUnhealthySecondThreshold = &value
	return b
}

// WithDeploymentUnhealthySecondThreshold sets the DeploymentUnhealthySecondThreshold field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeploymentUnhealthySecondThreshold field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithDeploymentUnhealthySecondThreshold(value int32) *RayServiceSpecApplyConfiguration {
	b.DeploymentUnhealthySecondThreshold = &value
	return b
}

// WithServeService sets the ServeService field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ServeService field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithServeService(value corev1.Service) *RayServiceSpecApplyConfiguration {
	b.ServeService = &value
	return b
}

// WithUpgradeStrategy sets the UpgradeStrategy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UpgradeStrategy field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithUpgradeStrategy(value *RayServiceUpgradeStrategyApplyConfiguration) *RayServiceSpecApplyConfiguration {
	b.UpgradeStrategy = value
	return b
}

// WithServeConfigV2 sets the ServeConfigV2 field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ServeConfigV2 field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithServeConfigV2(value string) *RayServiceSpecApplyConfiguration {
	b.ServeConfigV2 = &value
	return b
}

// WithRayClusterSpec sets the RayClusterSpec field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterSpec field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithRayClusterSpec(value *RayClusterSpecApplyConfiguration) *RayServiceSpecApplyConfiguration {
	b.RayClusterSpec = value
	return b
}

// WithExcludeHeadPodFromServeSvc sets the ExcludeHeadPodFromServeSvc field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ExcludeHeadPodFromServeSvc field is set to the value of the last call.
func (b *RayServiceSpecApplyConfiguration) WithExcludeHeadPodFromServeSvc(value bool) *RayServiceSpecApplyConfiguration {
	b.ExcludeHeadPodFromServeSvc = &value
	return b
}
