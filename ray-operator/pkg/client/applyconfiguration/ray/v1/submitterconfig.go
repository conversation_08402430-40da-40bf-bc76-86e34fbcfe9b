// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// SubmitterConfigApplyConfiguration represents a declarative configuration of the SubmitterConfig type for use
// with apply.
type SubmitterConfigApplyConfiguration struct {
	BackoffLimit *int32 `json:"backoffLimit,omitempty"`
}

// SubmitterConfigApplyConfiguration constructs a declarative configuration of the SubmitterConfig type for use with
// apply.
func SubmitterConfig() *SubmitterConfigApplyConfiguration {
	return &SubmitterConfigApplyConfiguration{}
}

// WithBackoffLimit sets the BackoffLimit field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the BackoffLimit field is set to the value of the last call.
func (b *SubmitterConfigApplyConfiguration) WithBackoffLimit(value int32) *SubmitterConfigApplyConfiguration {
	b.BackoffLimit = &value
	return b
}
