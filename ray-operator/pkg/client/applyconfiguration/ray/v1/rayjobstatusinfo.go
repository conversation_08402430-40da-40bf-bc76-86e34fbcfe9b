// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// RayJobStatusInfoApplyConfiguration represents a declarative configuration of the RayJobStatusInfo type for use
// with apply.
type RayJobStatusInfoApplyConfiguration struct {
	StartTime *metav1.Time `json:"startTime,omitempty"`
	EndTime   *metav1.Time `json:"endTime,omitempty"`
}

// RayJobStatusInfoApplyConfiguration constructs a declarative configuration of the RayJobStatusInfo type for use with
// apply.
func RayJobStatusInfo() *RayJobStatusInfoApplyConfiguration {
	return &RayJobStatusInfoApplyConfiguration{}
}

// WithStartTime sets the StartTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StartTime field is set to the value of the last call.
func (b *RayJobStatusInfoApplyConfiguration) WithStartTime(value metav1.Time) *RayJobStatusInfoApplyConfiguration {
	b.StartTime = &value
	return b
}

// WithEndTime sets the EndTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EndTime field is set to the value of the last call.
func (b *RayJobStatusInfoApplyConfiguration) WithEndTime(value metav1.Time) *RayJobStatusInfoApplyConfiguration {
	b.EndTime = &value
	return b
}
