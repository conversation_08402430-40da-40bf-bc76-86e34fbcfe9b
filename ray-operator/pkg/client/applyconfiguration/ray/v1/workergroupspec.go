// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	corev1 "k8s.io/client-go/applyconfigurations/core/v1"
)

// WorkerGroupSpecApplyConfiguration represents a declarative configuration of the WorkerGroupSpec type for use
// with apply.
type WorkerGroupSpecApplyConfiguration struct {
	Suspend            *bool                                     `json:"suspend,omitempty"`
	GroupName          *string                                   `json:"groupName,omitempty"`
	Replicas           *int32                                    `json:"replicas,omitempty"`
	MinReplicas        *int32                                    `json:"minReplicas,omitempty"`
	MaxReplicas        *int32                                    `json:"maxReplicas,omitempty"`
	IdleTimeoutSeconds *int32                                    `json:"idleTimeoutSeconds,omitempty"`
	RayStartParams     map[string]string                         `json:"rayStartParams,omitempty"`
	Template           *corev1.PodTemplateSpecApplyConfiguration `json:"template,omitempty"`
	ScaleStrategy      *ScaleStrategyApplyConfiguration          `json:"scaleStrategy,omitempty"`
	NumOfHosts         *int32                                    `json:"numOfHosts,omitempty"`
}

// WorkerGroupSpecApplyConfiguration constructs a declarative configuration of the WorkerGroupSpec type for use with
// apply.
func WorkerGroupSpec() *WorkerGroupSpecApplyConfiguration {
	return &WorkerGroupSpecApplyConfiguration{}
}

// WithSuspend sets the Suspend field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Suspend field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithSuspend(value bool) *WorkerGroupSpecApplyConfiguration {
	b.Suspend = &value
	return b
}

// WithGroupName sets the GroupName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the GroupName field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithGroupName(value string) *WorkerGroupSpecApplyConfiguration {
	b.GroupName = &value
	return b
}

// WithReplicas sets the Replicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Replicas field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithReplicas(value int32) *WorkerGroupSpecApplyConfiguration {
	b.Replicas = &value
	return b
}

// WithMinReplicas sets the MinReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MinReplicas field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithMinReplicas(value int32) *WorkerGroupSpecApplyConfiguration {
	b.MinReplicas = &value
	return b
}

// WithMaxReplicas sets the MaxReplicas field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MaxReplicas field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithMaxReplicas(value int32) *WorkerGroupSpecApplyConfiguration {
	b.MaxReplicas = &value
	return b
}

// WithIdleTimeoutSeconds sets the IdleTimeoutSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the IdleTimeoutSeconds field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithIdleTimeoutSeconds(value int32) *WorkerGroupSpecApplyConfiguration {
	b.IdleTimeoutSeconds = &value
	return b
}

// WithRayStartParams puts the entries into the RayStartParams field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the RayStartParams field,
// overwriting an existing map entries in RayStartParams field with the same key.
func (b *WorkerGroupSpecApplyConfiguration) WithRayStartParams(entries map[string]string) *WorkerGroupSpecApplyConfiguration {
	if b.RayStartParams == nil && len(entries) > 0 {
		b.RayStartParams = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.RayStartParams[k] = v
	}
	return b
}

// WithTemplate sets the Template field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Template field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithTemplate(value *corev1.PodTemplateSpecApplyConfiguration) *WorkerGroupSpecApplyConfiguration {
	b.Template = value
	return b
}

// WithScaleStrategy sets the ScaleStrategy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ScaleStrategy field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithScaleStrategy(value *ScaleStrategyApplyConfiguration) *WorkerGroupSpecApplyConfiguration {
	b.ScaleStrategy = value
	return b
}

// WithNumOfHosts sets the NumOfHosts field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the NumOfHosts field is set to the value of the last call.
func (b *WorkerGroupSpecApplyConfiguration) WithNumOfHosts(value int32) *WorkerGroupSpecApplyConfiguration {
	b.NumOfHosts = &value
	return b
}
