// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// RayJobStatusApplyConfiguration represents a declarative configuration of the RayJobStatus type for use
// with apply.
type RayJobStatusApplyConfiguration struct {
	RayJobStatusInfo    *RayJobStatusInfoApplyConfiguration `json:"rayJobInfo,omitempty"`
	JobId               *string                             `json:"jobId,omitempty"`
	RayClusterName      *string                             `json:"rayClusterName,omitempty"`
	DashboardURL        *string                             `json:"dashboardURL,omitempty"`
	JobStatus           *rayv1.JobStatus                    `json:"jobStatus,omitempty"`
	JobDeploymentStatus *rayv1.JobDeploymentStatus          `json:"jobDeploymentStatus,omitempty"`
	Reason              *rayv1.JobFailedReason              `json:"reason,omitempty"`
	Message             *string                             `json:"message,omitempty"`
	StartTime           *metav1.Time                        `json:"startTime,omitempty"`
	EndTime             *metav1.Time                        `json:"endTime,omitempty"`
	Succeeded           *int32                              `json:"succeeded,omitempty"`
	Failed              *int32                              `json:"failed,omitempty"`
	RayClusterStatus    *RayClusterStatusApplyConfiguration `json:"rayClusterStatus,omitempty"`
	ObservedGeneration  *int64                              `json:"observedGeneration,omitempty"`
}

// RayJobStatusApplyConfiguration constructs a declarative configuration of the RayJobStatus type for use with
// apply.
func RayJobStatus() *RayJobStatusApplyConfiguration {
	return &RayJobStatusApplyConfiguration{}
}

// WithRayJobStatusInfo sets the RayJobStatusInfo field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayJobStatusInfo field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithRayJobStatusInfo(value *RayJobStatusInfoApplyConfiguration) *RayJobStatusApplyConfiguration {
	b.RayJobStatusInfo = value
	return b
}

// WithJobId sets the JobId field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JobId field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithJobId(value string) *RayJobStatusApplyConfiguration {
	b.JobId = &value
	return b
}

// WithRayClusterName sets the RayClusterName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterName field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithRayClusterName(value string) *RayJobStatusApplyConfiguration {
	b.RayClusterName = &value
	return b
}

// WithDashboardURL sets the DashboardURL field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DashboardURL field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithDashboardURL(value string) *RayJobStatusApplyConfiguration {
	b.DashboardURL = &value
	return b
}

// WithJobStatus sets the JobStatus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JobStatus field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithJobStatus(value rayv1.JobStatus) *RayJobStatusApplyConfiguration {
	b.JobStatus = &value
	return b
}

// WithJobDeploymentStatus sets the JobDeploymentStatus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JobDeploymentStatus field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithJobDeploymentStatus(value rayv1.JobDeploymentStatus) *RayJobStatusApplyConfiguration {
	b.JobDeploymentStatus = &value
	return b
}

// WithReason sets the Reason field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Reason field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithReason(value rayv1.JobFailedReason) *RayJobStatusApplyConfiguration {
	b.Reason = &value
	return b
}

// WithMessage sets the Message field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Message field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithMessage(value string) *RayJobStatusApplyConfiguration {
	b.Message = &value
	return b
}

// WithStartTime sets the StartTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the StartTime field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithStartTime(value metav1.Time) *RayJobStatusApplyConfiguration {
	b.StartTime = &value
	return b
}

// WithEndTime sets the EndTime field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EndTime field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithEndTime(value metav1.Time) *RayJobStatusApplyConfiguration {
	b.EndTime = &value
	return b
}

// WithSucceeded sets the Succeeded field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Succeeded field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithSucceeded(value int32) *RayJobStatusApplyConfiguration {
	b.Succeeded = &value
	return b
}

// WithFailed sets the Failed field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Failed field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithFailed(value int32) *RayJobStatusApplyConfiguration {
	b.Failed = &value
	return b
}

// WithRayClusterStatus sets the RayClusterStatus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterStatus field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithRayClusterStatus(value *RayClusterStatusApplyConfiguration) *RayJobStatusApplyConfiguration {
	b.RayClusterStatus = value
	return b
}

// WithObservedGeneration sets the ObservedGeneration field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ObservedGeneration field is set to the value of the last call.
func (b *RayJobStatusApplyConfiguration) WithObservedGeneration(value int64) *RayJobStatusApplyConfiguration {
	b.ObservedGeneration = &value
	return b
}
