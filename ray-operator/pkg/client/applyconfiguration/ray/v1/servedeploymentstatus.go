// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ServeDeploymentStatusApplyConfiguration represents a declarative configuration of the ServeDeploymentStatus type for use
// with apply.
type ServeDeploymentStatusApplyConfiguration struct {
	Status  *string `json:"status,omitempty"`
	Message *string `json:"message,omitempty"`
}

// ServeDeploymentStatusApplyConfiguration constructs a declarative configuration of the ServeDeploymentStatus type for use with
// apply.
func ServeDeploymentStatus() *ServeDeploymentStatusApplyConfiguration {
	return &ServeDeploymentStatusApplyConfiguration{}
}

// WithStatus sets the Status field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Status field is set to the value of the last call.
func (b *ServeDeploymentStatusApplyConfiguration) WithStatus(value string) *ServeDeploymentStatusApplyConfiguration {
	b.Status = &value
	return b
}

// WithMessage sets the Message field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Message field is set to the value of the last call.
func (b *ServeDeploymentStatusApplyConfiguration) WithMessage(value string) *ServeDeploymentStatusApplyConfiguration {
	b.Message = &value
	return b
}
