// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// RayServiceStatusApplyConfiguration represents a declarative configuration of the RayServiceStatus type for use
// with apply.
type RayServiceStatusApplyConfiguration struct {
	Applications     map[string]AppStatusApplyConfiguration `json:"applicationStatuses,omitempty"`
	RayClusterName   *string                                `json:"rayClusterName,omitempty"`
	RayClusterStatus *RayClusterStatusApplyConfiguration    `json:"rayClusterStatus,omitempty"`
}

// RayServiceStatusApplyConfiguration constructs a declarative configuration of the RayServiceStatus type for use with
// apply.
func RayServiceStatus() *RayServiceStatusApplyConfiguration {
	return &RayServiceStatusApplyConfiguration{}
}

// WithApplications puts the entries into the Applications field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Applications field,
// overwriting an existing map entries in Applications field with the same key.
func (b *RayServiceStatusApplyConfiguration) WithApplications(entries map[string]AppStatusApplyConfiguration) *RayServiceStatusApplyConfiguration {
	if b.Applications == nil && len(entries) > 0 {
		b.Applications = make(map[string]AppStatusApplyConfiguration, len(entries))
	}
	for k, v := range entries {
		b.Applications[k] = v
	}
	return b
}

// WithRayClusterName sets the RayClusterName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterName field is set to the value of the last call.
func (b *RayServiceStatusApplyConfiguration) WithRayClusterName(value string) *RayServiceStatusApplyConfiguration {
	b.RayClusterName = &value
	return b
}

// WithRayClusterStatus sets the RayClusterStatus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterStatus field is set to the value of the last call.
func (b *RayServiceStatusApplyConfiguration) WithRayClusterStatus(value *RayClusterStatusApplyConfiguration) *RayServiceStatusApplyConfiguration {
	b.RayClusterStatus = value
	return b
}
