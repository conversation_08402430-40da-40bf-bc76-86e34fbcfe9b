// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	corev1 "k8s.io/client-go/applyconfigurations/core/v1"
)

// RayJobSpecApplyConfiguration represents a declarative configuration of the RayJobSpec type for use
// with apply.
type RayJobSpecApplyConfiguration struct {
	ActiveDeadlineSeconds    *int32                                    `json:"activeDeadlineSeconds,omitempty"`
	BackoffLimit             *int32                                    `json:"backoffLimit,omitempty"`
	RayClusterSpec           *RayClusterSpecApplyConfiguration         `json:"rayClusterSpec,omitempty"`
	SubmitterPodTemplate     *corev1.PodTemplateSpecApplyConfiguration `json:"submitterPodTemplate,omitempty"`
	Metadata                 map[string]string                         `json:"metadata,omitempty"`
	ClusterSelector          map[string]string                         `json:"clusterSelector,omitempty"`
	SubmitterConfig          *SubmitterConfigApplyConfiguration        `json:"submitterConfig,omitempty"`
	ManagedBy                *string                                   `json:"managedBy,omitempty"`
	DeletionStrategy         *DeletionStrategyApplyConfiguration       `json:"deletionStrategy,omitempty"`
	Entrypoint               *string                                   `json:"entrypoint,omitempty"`
	RuntimeEnvYAML           *string                                   `json:"runtimeEnvYAML,omitempty"`
	JobId                    *string                                   `json:"jobId,omitempty"`
	SubmissionMode           *rayv1.JobSubmissionMode                  `json:"submissionMode,omitempty"`
	EntrypointResources      *string                                   `json:"entrypointResources,omitempty"`
	EntrypointNumCpus        *float32                                  `json:"entrypointNumCpus,omitempty"`
	EntrypointNumGpus        *float32                                  `json:"entrypointNumGpus,omitempty"`
	TTLSecondsAfterFinished  *int32                                    `json:"ttlSecondsAfterFinished,omitempty"`
	ShutdownAfterJobFinishes *bool                                     `json:"shutdownAfterJobFinishes,omitempty"`
	Suspend                  *bool                                     `json:"suspend,omitempty"`
}

// RayJobSpecApplyConfiguration constructs a declarative configuration of the RayJobSpec type for use with
// apply.
func RayJobSpec() *RayJobSpecApplyConfiguration {
	return &RayJobSpecApplyConfiguration{}
}

// WithActiveDeadlineSeconds sets the ActiveDeadlineSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ActiveDeadlineSeconds field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithActiveDeadlineSeconds(value int32) *RayJobSpecApplyConfiguration {
	b.ActiveDeadlineSeconds = &value
	return b
}

// WithBackoffLimit sets the BackoffLimit field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the BackoffLimit field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithBackoffLimit(value int32) *RayJobSpecApplyConfiguration {
	b.BackoffLimit = &value
	return b
}

// WithRayClusterSpec sets the RayClusterSpec field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RayClusterSpec field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithRayClusterSpec(value *RayClusterSpecApplyConfiguration) *RayJobSpecApplyConfiguration {
	b.RayClusterSpec = value
	return b
}

// WithSubmitterPodTemplate sets the SubmitterPodTemplate field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SubmitterPodTemplate field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithSubmitterPodTemplate(value *corev1.PodTemplateSpecApplyConfiguration) *RayJobSpecApplyConfiguration {
	b.SubmitterPodTemplate = value
	return b
}

// WithMetadata puts the entries into the Metadata field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the Metadata field,
// overwriting an existing map entries in Metadata field with the same key.
func (b *RayJobSpecApplyConfiguration) WithMetadata(entries map[string]string) *RayJobSpecApplyConfiguration {
	if b.Metadata == nil && len(entries) > 0 {
		b.Metadata = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.Metadata[k] = v
	}
	return b
}

// WithClusterSelector puts the entries into the ClusterSelector field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, the entries provided by each call will be put on the ClusterSelector field,
// overwriting an existing map entries in ClusterSelector field with the same key.
func (b *RayJobSpecApplyConfiguration) WithClusterSelector(entries map[string]string) *RayJobSpecApplyConfiguration {
	if b.ClusterSelector == nil && len(entries) > 0 {
		b.ClusterSelector = make(map[string]string, len(entries))
	}
	for k, v := range entries {
		b.ClusterSelector[k] = v
	}
	return b
}

// WithSubmitterConfig sets the SubmitterConfig field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SubmitterConfig field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithSubmitterConfig(value *SubmitterConfigApplyConfiguration) *RayJobSpecApplyConfiguration {
	b.SubmitterConfig = value
	return b
}

// WithManagedBy sets the ManagedBy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ManagedBy field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithManagedBy(value string) *RayJobSpecApplyConfiguration {
	b.ManagedBy = &value
	return b
}

// WithDeletionStrategy sets the DeletionStrategy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the DeletionStrategy field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithDeletionStrategy(value *DeletionStrategyApplyConfiguration) *RayJobSpecApplyConfiguration {
	b.DeletionStrategy = value
	return b
}

// WithEntrypoint sets the Entrypoint field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Entrypoint field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithEntrypoint(value string) *RayJobSpecApplyConfiguration {
	b.Entrypoint = &value
	return b
}

// WithRuntimeEnvYAML sets the RuntimeEnvYAML field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the RuntimeEnvYAML field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithRuntimeEnvYAML(value string) *RayJobSpecApplyConfiguration {
	b.RuntimeEnvYAML = &value
	return b
}

// WithJobId sets the JobId field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the JobId field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithJobId(value string) *RayJobSpecApplyConfiguration {
	b.JobId = &value
	return b
}

// WithSubmissionMode sets the SubmissionMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SubmissionMode field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithSubmissionMode(value rayv1.JobSubmissionMode) *RayJobSpecApplyConfiguration {
	b.SubmissionMode = &value
	return b
}

// WithEntrypointResources sets the EntrypointResources field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EntrypointResources field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithEntrypointResources(value string) *RayJobSpecApplyConfiguration {
	b.EntrypointResources = &value
	return b
}

// WithEntrypointNumCpus sets the EntrypointNumCpus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EntrypointNumCpus field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithEntrypointNumCpus(value float32) *RayJobSpecApplyConfiguration {
	b.EntrypointNumCpus = &value
	return b
}

// WithEntrypointNumGpus sets the EntrypointNumGpus field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the EntrypointNumGpus field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithEntrypointNumGpus(value float32) *RayJobSpecApplyConfiguration {
	b.EntrypointNumGpus = &value
	return b
}

// WithTTLSecondsAfterFinished sets the TTLSecondsAfterFinished field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the TTLSecondsAfterFinished field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithTTLSecondsAfterFinished(value int32) *RayJobSpecApplyConfiguration {
	b.TTLSecondsAfterFinished = &value
	return b
}

// WithShutdownAfterJobFinishes sets the ShutdownAfterJobFinishes field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ShutdownAfterJobFinishes field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithShutdownAfterJobFinishes(value bool) *RayJobSpecApplyConfiguration {
	b.ShutdownAfterJobFinishes = &value
	return b
}

// WithSuspend sets the Suspend field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Suspend field is set to the value of the last call.
func (b *RayJobSpecApplyConfiguration) WithSuspend(value bool) *RayJobSpecApplyConfiguration {
	b.Suspend = &value
	return b
}
