// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// HeadInfoApplyConfiguration represents a declarative configuration of the HeadInfo type for use
// with apply.
type HeadInfoApplyConfiguration struct {
	PodIP       *string `json:"podIP,omitempty"`
	ServiceIP   *string `json:"serviceIP,omitempty"`
	PodName     *string `json:"podName,omitempty"`
	ServiceName *string `json:"serviceName,omitempty"`
}

// HeadInfoApplyConfiguration constructs a declarative configuration of the HeadInfo type for use with
// apply.
func HeadInfo() *HeadInfoApplyConfiguration {
	return &HeadInfoApplyConfiguration{}
}

// WithPodIP sets the PodIP field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PodIP field is set to the value of the last call.
func (b *HeadInfoApplyConfiguration) WithPodIP(value string) *HeadInfoApplyConfiguration {
	b.PodIP = &value
	return b
}

// WithServiceIP sets the ServiceIP field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ServiceIP field is set to the value of the last call.
func (b *HeadInfoApplyConfiguration) WithServiceIP(value string) *HeadInfoApplyConfiguration {
	b.ServiceIP = &value
	return b
}

// WithPodName sets the PodName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PodName field is set to the value of the last call.
func (b *HeadInfoApplyConfiguration) WithPodName(value string) *HeadInfoApplyConfiguration {
	b.PodName = &value
	return b
}

// WithServiceName sets the ServiceName field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ServiceName field is set to the value of the last call.
func (b *HeadInfoApplyConfiguration) WithServiceName(value string) *HeadInfoApplyConfiguration {
	b.ServiceName = &value
	return b
}
