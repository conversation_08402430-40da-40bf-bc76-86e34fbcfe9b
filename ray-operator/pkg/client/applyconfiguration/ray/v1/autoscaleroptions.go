// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	corev1 "k8s.io/api/core/v1"
)

// AutoscalerOptionsApplyConfiguration represents a declarative configuration of the AutoscalerOptions type for use
// with apply.
type AutoscalerOptionsApplyConfiguration struct {
	Resources          *corev1.ResourceRequirements `json:"resources,omitempty"`
	Image              *string                      `json:"image,omitempty"`
	ImagePullPolicy    *corev1.PullPolicy           `json:"imagePullPolicy,omitempty"`
	SecurityContext    *corev1.SecurityContext      `json:"securityContext,omitempty"`
	IdleTimeoutSeconds *int32                       `json:"idleTimeoutSeconds,omitempty"`
	UpscalingMode      *rayv1.UpscalingMode         `json:"upscalingMode,omitempty"`
	Version            *rayv1.AutoscalerVersion     `json:"version,omitempty"`
	Env                []corev1.EnvVar              `json:"env,omitempty"`
	EnvFrom            []corev1.EnvFromSource       `json:"envFrom,omitempty"`
	VolumeMounts       []corev1.VolumeMount         `json:"volumeMounts,omitempty"`
}

// AutoscalerOptionsApplyConfiguration constructs a declarative configuration of the AutoscalerOptions type for use with
// apply.
func AutoscalerOptions() *AutoscalerOptionsApplyConfiguration {
	return &AutoscalerOptionsApplyConfiguration{}
}

// WithResources sets the Resources field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Resources field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithResources(value corev1.ResourceRequirements) *AutoscalerOptionsApplyConfiguration {
	b.Resources = &value
	return b
}

// WithImage sets the Image field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Image field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithImage(value string) *AutoscalerOptionsApplyConfiguration {
	b.Image = &value
	return b
}

// WithImagePullPolicy sets the ImagePullPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the ImagePullPolicy field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithImagePullPolicy(value corev1.PullPolicy) *AutoscalerOptionsApplyConfiguration {
	b.ImagePullPolicy = &value
	return b
}

// WithSecurityContext sets the SecurityContext field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the SecurityContext field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithSecurityContext(value corev1.SecurityContext) *AutoscalerOptionsApplyConfiguration {
	b.SecurityContext = &value
	return b
}

// WithIdleTimeoutSeconds sets the IdleTimeoutSeconds field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the IdleTimeoutSeconds field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithIdleTimeoutSeconds(value int32) *AutoscalerOptionsApplyConfiguration {
	b.IdleTimeoutSeconds = &value
	return b
}

// WithUpscalingMode sets the UpscalingMode field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the UpscalingMode field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithUpscalingMode(value rayv1.UpscalingMode) *AutoscalerOptionsApplyConfiguration {
	b.UpscalingMode = &value
	return b
}

// WithVersion sets the Version field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Version field is set to the value of the last call.
func (b *AutoscalerOptionsApplyConfiguration) WithVersion(value rayv1.AutoscalerVersion) *AutoscalerOptionsApplyConfiguration {
	b.Version = &value
	return b
}

// WithEnv adds the given value to the Env field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the Env field.
func (b *AutoscalerOptionsApplyConfiguration) WithEnv(values ...corev1.EnvVar) *AutoscalerOptionsApplyConfiguration {
	for i := range values {
		b.Env = append(b.Env, values[i])
	}
	return b
}

// WithEnvFrom adds the given value to the EnvFrom field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the EnvFrom field.
func (b *AutoscalerOptionsApplyConfiguration) WithEnvFrom(values ...corev1.EnvFromSource) *AutoscalerOptionsApplyConfiguration {
	for i := range values {
		b.EnvFrom = append(b.EnvFrom, values[i])
	}
	return b
}

// WithVolumeMounts adds the given value to the VolumeMounts field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the VolumeMounts field.
func (b *AutoscalerOptionsApplyConfiguration) WithVolumeMounts(values ...corev1.VolumeMount) *AutoscalerOptionsApplyConfiguration {
	for i := range values {
		b.VolumeMounts = append(b.VolumeMounts, values[i])
	}
	return b
}
