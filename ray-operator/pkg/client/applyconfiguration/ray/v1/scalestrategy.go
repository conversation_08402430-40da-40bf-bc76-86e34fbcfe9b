// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1

// ScaleStrategyApplyConfiguration represents a declarative configuration of the ScaleStrategy type for use
// with apply.
type ScaleStrategyApplyConfiguration struct {
	WorkersToDelete []string `json:"workersToDelete,omitempty"`
}

// ScaleStrategyApplyConfiguration constructs a declarative configuration of the ScaleStrategy type for use with
// apply.
func ScaleStrategy() *ScaleStrategyApplyConfiguration {
	return &ScaleStrategyApplyConfiguration{}
}

// WithWorkersToDelete adds the given value to the WorkersToDelete field in the declarative configuration
// and returns the receiver, so that objects can be build by chaining "With" function invocations.
// If called multiple times, values provided by each call will be appended to the WorkersToDelete field.
func (b *ScaleStrategyApplyConfiguration) WithWorkersToDelete(values ...string) *ScaleStrategyApplyConfiguration {
	for i := range values {
		b.WorkersToDelete = append(b.WorkersToDelete, values[i])
	}
	return b
}
