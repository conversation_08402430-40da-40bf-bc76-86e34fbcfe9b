// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// RayServiceLister helps list RayServices.
// All objects returned here must be treated as read-only.
type RayServiceLister interface {
	// List lists all RayServices in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayService, err error)
	// RayServices returns an object that can list and get RayServices.
	RayServices(namespace string) RayServiceNamespaceLister
	RayServiceListerExpansion
}

// rayServiceLister implements the RayServiceLister interface.
type rayServiceLister struct {
	listers.ResourceIndexer[*rayv1.RayService]
}

// NewRayServiceLister returns a new RayServiceLister.
func NewRayServiceLister(indexer cache.Indexer) RayServiceLister {
	return &rayServiceLister{listers.New[*rayv1.RayService](indexer, rayv1.Resource("rayservice"))}
}

// RayServices returns an object that can list and get RayServices.
func (s *rayServiceLister) RayServices(namespace string) RayServiceNamespaceLister {
	return rayServiceNamespaceLister{listers.NewNamespaced[*rayv1.RayService](s.ResourceIndexer, namespace)}
}

// RayServiceNamespaceLister helps list and get RayServices.
// All objects returned here must be treated as read-only.
type RayServiceNamespaceLister interface {
	// List lists all RayServices in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayService, err error)
	// Get retrieves the RayService from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*rayv1.RayService, error)
	RayServiceNamespaceListerExpansion
}

// rayServiceNamespaceLister implements the RayServiceNamespaceLister
// interface.
type rayServiceNamespaceLister struct {
	listers.ResourceIndexer[*rayv1.RayService]
}
