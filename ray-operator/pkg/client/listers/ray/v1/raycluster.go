// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// RayClusterLister helps list RayClusters.
// All objects returned here must be treated as read-only.
type RayClusterLister interface {
	// List lists all RayClusters in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayCluster, err error)
	// RayClusters returns an object that can list and get RayClusters.
	RayClusters(namespace string) RayClusterNamespaceLister
	RayClusterListerExpansion
}

// rayClusterLister implements the RayClusterLister interface.
type rayClusterLister struct {
	listers.ResourceIndexer[*rayv1.RayCluster]
}

// NewRayClusterLister returns a new RayClusterLister.
func NewRayClusterLister(indexer cache.Indexer) RayClusterLister {
	return &rayClusterLister{listers.New[*rayv1.RayCluster](indexer, rayv1.Resource("raycluster"))}
}

// RayClusters returns an object that can list and get RayClusters.
func (s *rayClusterLister) RayClusters(namespace string) RayClusterNamespaceLister {
	return rayClusterNamespaceLister{listers.NewNamespaced[*rayv1.RayCluster](s.ResourceIndexer, namespace)}
}

// RayClusterNamespaceLister helps list and get RayClusters.
// All objects returned here must be treated as read-only.
type RayClusterNamespaceLister interface {
	// List lists all RayClusters in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayCluster, err error)
	// Get retrieves the RayCluster from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*rayv1.RayCluster, error)
	RayClusterNamespaceListerExpansion
}

// rayClusterNamespaceLister implements the RayClusterNamespaceLister
// interface.
type rayClusterNamespaceLister struct {
	listers.ResourceIndexer[*rayv1.RayCluster]
}
