// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// RayJobLister helps list RayJobs.
// All objects returned here must be treated as read-only.
type RayJobLister interface {
	// List lists all RayJobs in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayJob, err error)
	// RayJobs returns an object that can list and get RayJobs.
	RayJobs(namespace string) RayJobNamespaceLister
	RayJobListerExpansion
}

// rayJobLister implements the RayJobLister interface.
type rayJobLister struct {
	listers.ResourceIndexer[*rayv1.RayJob]
}

// NewRayJobLister returns a new RayJobLister.
func NewRayJobLister(indexer cache.Indexer) RayJobLister {
	return &rayJobLister{listers.New[*rayv1.RayJob](indexer, rayv1.Resource("rayjob"))}
}

// RayJobs returns an object that can list and get RayJobs.
func (s *rayJobLister) RayJobs(namespace string) RayJobNamespaceLister {
	return rayJobNamespaceLister{listers.NewNamespaced[*rayv1.RayJob](s.ResourceIndexer, namespace)}
}

// RayJobNamespaceLister helps list and get RayJobs.
// All objects returned here must be treated as read-only.
type RayJobNamespaceLister interface {
	// List lists all RayJobs in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*rayv1.RayJob, err error)
	// Get retrieves the RayJob from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*rayv1.RayJob, error)
	RayJobNamespaceListerExpansion
}

// rayJobNamespaceLister implements the RayJobNamespaceLister
// interface.
type rayJobNamespaceLister struct {
	listers.ResourceIndexer[*rayv1.RayJob]
}
