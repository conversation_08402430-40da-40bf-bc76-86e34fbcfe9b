// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/ray-project/kuberay/ray-operator/controllers/ray/metrics (interfaces: RayJobMetricsObserver)
//
// Generated by this command:
//
//	mockgen -destination=mocks/ray_job_metrics_mock.go -package=mocks github.com/ray-project/kuberay/ray-operator/controllers/ray/metrics RayJobMetricsObserver
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	v1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRayJobMetricsObserver is a mock of RayJobMetricsObserver interface.
type MockRayJobMetricsObserver struct {
	ctrl     *gomock.Controller
	recorder *MockRayJobMetricsObserverMockRecorder
	isgomock struct{}
}

// MockRayJobMetricsObserverMockRecorder is the mock recorder for MockRayJobMetricsObserver.
type MockRayJobMetricsObserverMockRecorder struct {
	mock *MockRayJobMetricsObserver
}

// NewMockRayJobMetricsObserver creates a new mock instance.
func NewMockRayJobMetricsObserver(ctrl *gomock.Controller) *MockRayJobMetricsObserver {
	mock := &MockRayJobMetricsObserver{ctrl: ctrl}
	mock.recorder = &MockRayJobMetricsObserverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRayJobMetricsObserver) EXPECT() *MockRayJobMetricsObserverMockRecorder {
	return m.recorder
}

// ObserveRayJobExecutionDuration mocks base method.
func (m *MockRayJobMetricsObserver) ObserveRayJobExecutionDuration(name, namespace string, jobDeploymentStatus v1.JobDeploymentStatus, retryCount int, duration float64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ObserveRayJobExecutionDuration", name, namespace, jobDeploymentStatus, retryCount, duration)
}

// ObserveRayJobExecutionDuration indicates an expected call of ObserveRayJobExecutionDuration.
func (mr *MockRayJobMetricsObserverMockRecorder) ObserveRayJobExecutionDuration(name, namespace, jobDeploymentStatus, retryCount, duration any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObserveRayJobExecutionDuration", reflect.TypeOf((*MockRayJobMetricsObserver)(nil).ObserveRayJobExecutionDuration), name, namespace, jobDeploymentStatus, retryCount, duration)
}
