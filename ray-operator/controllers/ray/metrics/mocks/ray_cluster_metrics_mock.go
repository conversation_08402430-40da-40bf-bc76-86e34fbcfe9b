// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/ray-project/kuberay/ray-operator/controllers/ray/metrics (interfaces: RayClusterMetricsObserver)
//
// Generated by this command:
//
//	mockgen -destination=mocks/ray_cluster_metrics_mock.go -package=mocks github.com/ray-project/kuberay/ray-operator/controllers/ray/metrics RayClusterMetricsObserver
//

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRayClusterMetricsObserver is a mock of RayClusterMetricsObserver interface.
type MockRayClusterMetricsObserver struct {
	ctrl     *gomock.Controller
	recorder *MockRayClusterMetricsObserverMockRecorder
	isgomock struct{}
}

// MockRayClusterMetricsObserverMockRecorder is the mock recorder for MockRayClusterMetricsObserver.
type MockRayClusterMetricsObserverMockRecorder struct {
	mock *MockRayClusterMetricsObserver
}

// NewMockRayClusterMetricsObserver creates a new mock instance.
func NewMockRayClusterMetricsObserver(ctrl *gomock.Controller) *MockRayClusterMetricsObserver {
	mock := &MockRayClusterMetricsObserver{ctrl: ctrl}
	mock.recorder = &MockRayClusterMetricsObserverMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRayClusterMetricsObserver) EXPECT() *MockRayClusterMetricsObserverMockRecorder {
	return m.recorder
}

// ObserveRayClusterProvisionedDuration mocks base method.
func (m *MockRayClusterMetricsObserver) ObserveRayClusterProvisionedDuration(name, namespace string, duration float64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ObserveRayClusterProvisionedDuration", name, namespace, duration)
}

// ObserveRayClusterProvisionedDuration indicates an expected call of ObserveRayClusterProvisionedDuration.
func (mr *MockRayClusterMetricsObserverMockRecorder) ObserveRayClusterProvisionedDuration(name, namespace, duration any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObserveRayClusterProvisionedDuration", reflect.TypeOf((*MockRayClusterMetricsObserver)(nil).ObserveRayClusterProvisionedDuration), name, namespace, duration)
}
