apiVersion: ray.io/v1
kind: RayJob
metadata:
  name: {{.Name}}
  labels:
    perf-test: rayjob-pytorch-mnist
spec:
  shutdownAfterJobFinishes: true
  entrypoint: python ray_train_pytorch_mnist.py
  runtimeEnvYAML: |
    env_vars:
      NUM_WORKERS: "2"
      CPUS_PER_WORKER: "1"
      OMP_NUM_THREADS: "1" # Set OMP_NUM_THREADS to avoid KeyErorr race condition.
  rayClusterSpec:
    rayVersion: '2.46.0'
    headGroupSpec:
      template:
        spec:
          containers:
            - name: ray-head
              image: {{.Image}}
              ports:
                - containerPort: 6379
                  name: gcs-server
                - containerPort: 8265
                  name: dashboard
                - containerPort: 10001
                  name: client
              resources:
                limits:
                  memory: "4Gi"
                requests:
                  cpu: "1"
                  memory: "4Gi"
    workerGroupSpecs:
      - replicas: 2
        minReplicas: 1
        maxReplicas: 5
        groupName: worker-group
        template:
          spec:
            containers:
              - name: ray-worker
                image: {{.Image}}
                resources:
                  limits:
                    memory: "4Gi"
                  requests:
                    cpu: "1"
                    memory: "4Gi"
