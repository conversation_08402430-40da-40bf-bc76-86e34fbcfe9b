name: kuberay
namespace:
  number: 100
tuningSets:
- name: Uniform100qps
  qpsLoad:
    qps: 100
steps:
- name: Start measurements
  measurements:
  - Identifier: PodStartupLatency
    Method: PodStartupLatency
    Params:
      action: start
      labelSelector: app.kubernetes.io/created-by = kuberay-operator
      threshold: 30m
  - Identifier: WaitForControlledPodsRunning
    Method: WaitForControlledPodsRunning
    Params:
      action: start
      apiVersion: ray.io/v1
      kind: RayCluster
      labelSelector: app.kubernetes.io/created-by = kuberay-operator
      operationTimeout: 120s
- name: Preload Images
  measurements:
  - Identifier: PreloadImages
    Method: Exec
    Params:
      timeout: 30m
      command:
      - "bash"
      - "common/preload-image.sh"
- name: Creating Ray clusters
  phases:
  - namespaceRange:
      min: 1
      max: 100
    replicasPerNamespace: 100
    tuningSet: Uniform100qps
    objectBundle:
    - basename: raycluster
      objectTemplatePath: raycluster.yaml
      templateFillMap:
        Replicas: 3
        Image: "rayproject/ray:2.46.0"
- name: Wait for RayClusters ready
  measurements:
  - Identifier: WaitForRayCluster
    Method: Exec
    Params:
      timeout: 30m
      command:
      - "bash"
      - "10000-raycluster/wait-for-rayclusters.sh"
      - "10000"
- name: Measure wait for pods to be running
  measurements:
  - Identifier: WaitForControlledPodsRunning
    Method: WaitForControlledPodsRunning
    Params:
      action: gather
- name: Measure pod startup latency
  measurements:
  - Identifier: PodStartupLatency
    Method: PodStartupLatency
    Params:
      action: gather
