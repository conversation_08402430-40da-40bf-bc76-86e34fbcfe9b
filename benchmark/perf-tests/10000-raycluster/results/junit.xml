<testsuite name="ClusterLoaderV2" tests="0" failures="0" errors="0" time="6459.44">
    <testcase name="kuberay overall (10000-raycluster/config.yaml)" classname="ClusterLoaderV2" time="2720.126789555"/>
    <testcase name="kuberay: [step: 01] Start measurements [00] - PodStartupLatency" classname="ClusterLoaderV2" time="0.125443516"/>
    <testcase name="kuberay: [step: 01] Start measurements [01] - WaitForControlledPodsRunning" classname="ClusterLoaderV2" time="7.506402189"/>
    <testcase name="kuberay: [step: 02] Preload Images [00] - PreloadImages" classname="ClusterLoaderV2" time="1613.557372951"/>
    <testcase name="kuberay: [step: 03] Creating Ray clusters" classname="ClusterLoaderV2" time="88.845644414"/>
    <testcase name="kuberay: [step: 04] Wait for RayClusters ready [00] - WaitForRayCluster" classname="ClusterLoaderV2" time="1515.125130335"/>
    <testcase name="kuberay: [step: 05] Wait for pods to be running [00] - WaitForControlledPodsRunning" classname="ClusterLoaderV2" time="8.886798602"/>
    <testcase name="kuberay: [step: 06] Measure pod startup latency [00] - PodStartupLatency" classname="ClusterLoaderV2" time="14.67322536"/>
</testsuite>
