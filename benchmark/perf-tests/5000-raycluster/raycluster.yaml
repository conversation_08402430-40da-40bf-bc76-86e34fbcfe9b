apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: {{.Name}}
  labels:
    perf-test: ray-cluster
spec:
  rayVersion: '2.46.0'
  headGroupSpec:
    serviceType: ClusterIP
    rayStartParams:
      dashboard-host: '0.0.0.0'
      disable-usage-stats: 'true'
    template:
      spec:
        containers:
        - name: ray-head
          image: {{.Image}}
          ports:
          - containerPort: 6379
            name: gcs
          - containerPort: 8265
            name: dashboard
          - containerPort: 10001
            name: client
          resources:
            limits:
              cpu: "1"
            requests:
              cpu: "10m"
        volumes:
          - name: ray-logs
            emptyDir: {}
  workerGroupSpecs:
  - replicas: {{.Replicas}}
    minReplicas: 1
    maxReplicas: 10
    groupName: small-group
    template:
      spec:
        containers:
        - name: ray-worker
          image: {{.Image}}
          resources:
            limits:
              cpu: "1"
            requests:
              cpu: "10m"
