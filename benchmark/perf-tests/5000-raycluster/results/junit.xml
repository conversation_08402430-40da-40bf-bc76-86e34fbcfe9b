<testsuite name="ClusterLoaderV2" tests="0" failures="0" errors="0" time="5759.13">
    <testcase name="kuberay overall (5000-raycluster/config.yaml)" classname="ClusterLoaderV2" time="2700.130789055"/>
    <testcase name="kuberay: [step: 01] Start measurements [00] - PodStartupLatency" classname="ClusterLoaderV2" time="0.105403516"/>
    <testcase name="kuberay: [step: 01] Start measurements [01] - WaitForControlledPodsRunning" classname="ClusterLoaderV2" time="5.306202189"/>
    <testcase name="kuberay: [step: 02] Preload Images [00] - PreloadImages" classname="ClusterLoaderV2" time="1216.597375951"/>
    <testcase name="kuberay: [step: 03] Creating Ray clusters" classname="ClusterLoaderV2" time="53.805694612"/>
    <testcase name="kuberay: [step: 04] Wait for RayClusters ready [00] - WaitForRayCluster" classname="ClusterLoaderV2" time="1213.128180545"/>
    <testcase name="kuberay: [step: 05] Wait for pods to be running [00] - WaitForControlledPodsRunning" classname="ClusterLoaderV2" time="7.786798602"/>
    <testcase name="kuberay: [step: 06] Measure pod startup latency [00] - PodStartupLatency" classname="ClusterLoaderV2" time="11.77322536"/>
</testsuite>
