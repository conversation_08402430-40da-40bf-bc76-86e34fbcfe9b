apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ray-image-preloader
  labels:
    k8s-app: ray-image-preloader
spec:
  selector:
    matchLabels:
      k8s-app: ray-image-preloader
  template:
    metadata:
      labels:
        name: ray-image-preloader
        k8s-app: ray-image-preloader
    spec:
      containers:
      - image: {{.Image}}
        name: ray-image-preloader
        command: [ "sleep", "inf" ]
