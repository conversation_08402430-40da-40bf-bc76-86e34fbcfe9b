name: kuberay
namespace:
  number: 100
tuningSets:
- name: Uniform100qps
  qpsLoad:
    qps: 100
steps:
- name: Start measurements
  measurements:
  - Identifier: PodStartupLatency
    Method: PodStartupLatency
    Params:
      action: start
      labelSelector: app.kubernetes.io/created-by = kuberay-operator
      threshold: 30m
  - Identifier: WaitForControlledPodsRunning
    Method: WaitForControlledPodsRunning
    Params:
      action: start
      apiVersion: ray.io/v1
      kind: RayCluster
      labelSelector: app.kubernetes.io/created-by = kuberay-operator
      operationTimeout: 120s
  - Identifier: JobLifecycleLatency
    Method: JobLifecycleLatency
    Params:
      action: start
      labelSelector: app.kubernetes.io/created-by = kuberay-operator
      threshold: 10m
- name: Creating RayJobs for PyTorch MNIST fine-tuning
  phases:
  - namespaceRange:
      min: 1
      max: 100
    replicasPerNamespace: 25
    tuningSet: Uniform100qps
    objectBundle:
    - basename: pytorch-mnist
      objectTemplatePath: pytorch-mnist-rayjob.yaml
      Image: "rayproject/ray:2.46.0"
- name: Creating RayJobs for Ray Data Image Resizing
  phases:
  - namespaceRange:
      min: 1
      max: 100
    replicasPerNamespace: 25
    tuningSet: Uniform100qps
    objectBundle:
    - basename: ray-data-image-resize
      objectTemplatePath: ray-data-image-resize.yaml
      Image: "rayproject/ray:2.46.0"
- name: Wait for RayJobs complete
  measurements:
  - Identifier: WaitForRayJob
    Method: Exec
    Params:
      timeout: 60m
      command:
      - "bash"
      - "common/wait-for-rayjobs.sh"
      - "2500" # total 5000 since we deploy 2 RayJobs with 2500 instances each
- name: Measure wait for pods to be running
  measurements:
  - Identifier: WaitForControlledPodsRunning
    Method: WaitForControlledPodsRunning
    Params:
      action: gather
      operationTimeout: 10m
- name: Measure pod startup latency
  measurements:
  - Identifier: PodStartupLatency
    Method: PodStartupLatency
    Params:
      action: gather
- name: Measure job finished
  measurements:
  - Identifier: JobLifecycleLatency
    Method: JobLifecycleLatency
    Params:
      action: gather
