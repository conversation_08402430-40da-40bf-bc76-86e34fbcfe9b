apiVersion: ray.io/v1alpha1
kind: RayCluster
metadata:
  name: '$raycluster_name'
spec:
  rayVersion: '2.46.0'
  # Ray head pod template
  headGroupSpec:
    rayStartParams:
      dashboard-host: '0.0.0.0'
    template:
      spec:
        containers:
        - name: ray-head
          image: rayproject/ray:2.46.0
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 1
              memory: 1Gi
          ports:
          - containerPort: 6379
            name: gcs-server
          - containerPort: 8265
            name: dashboard
          - containerPort: 10001
            name: client
  workerGroupSpecs:
  - replicas: $num_worker_pods
    minReplicas: 0
    maxReplicas: 200
    groupName: small-group
    template:
      spec:
        containers:
        - name: ray-worker
          image: rayproject/ray:2.46.0
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","ray stop"]
          volumeMounts:
            - mountPath: /tmp/ray
              name: ray-logs
          resources:
            limits:
              cpu: "1"
              memory: "1G"
            requests:
              cpu: "1"
              memory: "1G"
        volumes:
          - name: ray-logs
            emptyDir: {}
