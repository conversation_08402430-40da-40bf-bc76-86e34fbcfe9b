- label: 'Test E2E (kubectl-plugin)'
  instance_size: large
  image: golang:1.24
  commands:
    - source .buildkite/setup-env.sh
    - kind create cluster --wait 900s --config ./ci/kind-config-buildkite.yml
    - kubectl config set clusters.kind-kind.server https://docker:6443
    # Deploy nightly KubeRay operator
    - echo Deploying Kuberay operator
    - pushd ray-operator
    - IMG=kuberay/operator:nightly make docker-image
    - kind load docker-image kuberay/operator:nightly
    - IMG=kuberay/operator:nightly make deploy
    - kubectl wait --timeout=90s --for=condition=Available=true deployment kuberay-operator
    - popd && pushd kubectl-plugin
    # Build CLI and add to path
    - go mod download
    - go build -o kubectl-ray -a ./cmd/kubectl-ray.go
    - cp ./kubectl-ray /usr/local/bin
    # Run e2e tests
    - echo "--- START:Running Test E2E (kubectl-plugin) tests"
    - set -o pipefail
    - KUBERAY_TEST_TIMEOUT_SHORT=1m KUBERAY_TEST_TIMEOUT_MEDIUM=5m KUBERAY_TEST_TIMEOUT_LONG=10m go test -timeout 60m -v ./test/e2e 2>&1 | awk -f ../.buildkite/format.awk
    - echo "--- END:Test E2E (kubectl-plugin) tests finished"
