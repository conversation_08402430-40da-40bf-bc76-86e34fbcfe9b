- label: 'Test Sample YAMLs (nightly operator)'
  instance_size: large
  image: golang:1.24
  commands:
    - source .buildkite/setup-env.sh
    - kind create cluster --wait 900s --config ./ci/kind-config-buildkite.yml
    - kubectl config set clusters.kind-kind.server https://docker:6443
    # Build nightly KubeRay operator image
    - pushd ray-operator
    - IMG=kuberay/operator:nightly make docker-image
    - kind load docker-image kuberay/operator:nightly
    - IMG=kuberay/operator:nightly make deploy
    - kubectl wait --timeout=90s --for=condition=Available=true deployment kuberay-operator
    # Run sample YAML tests
    - echo "--- START:Running Sample YAMLs (nightly operator) tests"
    - set -o pipefail
    - KUBERAY_TEST_TIMEOUT_SHORT=1m KUBERAY_TEST_TIMEOUT_MEDIUM=5m KUBERAY_TEST_TIMEOUT_LONG=10m go test -timeout 30m -v ./test/sampleyaml 2>&1 | awk -f ../.buildkite/format.awk
    - echo "--- END:Sample YAMLs (nightly operator) tests finished"
    # Printing KubeRay operator logs
    - kubectl logs --tail -1 -l app.kubernetes.io/name=kuberay

- label: 'Test Sample YAMLs (latest release)'
  instance_size: large
  image: golang:1.24
  commands:
    - source .buildkite/setup-env.sh
    - kind create cluster --wait 900s --config ./ci/kind-config-buildkite.yml
    - kubectl config set clusters.kind-kind.server https://docker:6443
    # Deploy KubeRay operator
    - pushd ray-operator
    - IMG=quay.io/kuberay/operator:v1.3.0 make deploy
    - kubectl wait --timeout=90s --for=condition=Available=true deployment kuberay-operator
    # Run sample YAML tests
    - echo "--- START:Running Sample YAMLs (latest release) tests"
    - set -o pipefail
    - KUBERAY_TEST_TIMEOUT_SHORT=1m KUBERAY_TEST_TIMEOUT_MEDIUM=5m KUBERAY_TEST_TIMEOUT_LONG=10m go test -timeout 30m -v ./test/sampleyaml 2>&1 | awk -f ../.buildkite/format.awk
    - echo "--- END:Sample YAMLs (latest release) tests finished"
    # Printing KubeRay operator logs
    - kubectl logs --tail -1 -l app.kubernetes.io/name=kuberay
